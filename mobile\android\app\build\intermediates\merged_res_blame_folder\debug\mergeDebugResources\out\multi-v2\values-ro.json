{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,363,464,585,669,735,830,904,964,1048,1114,1172,1245,1308,1364,1483,1540,1601,1657,1731,1876,1962,2046,2149,2231,2314,2404,2471,2537,2610,2688,2776,2847,2924,2998,3070,3161,3235,3330,3428,3502,3582,3683,3736,3802,3891,3981,4043,4107,4170,4282,4395,4505,4617", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "271,358,459,580,664,730,825,899,959,1043,1109,1167,1240,1303,1359,1478,1535,1596,1652,1726,1871,1957,2041,2144,2226,2309,2399,2466,2532,2605,2683,2771,2842,2919,2993,3065,3156,3230,3325,3423,3497,3577,3678,3731,3797,3886,3976,4038,4102,4165,4277,4390,4500,4612,4691"}, "to": {"startLines": "2,37,40,41,42,47,48,65,68,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3444,3729,3830,3951,4485,4551,6709,6938,6998,7082,7148,7206,7279,7342,7398,7517,7574,7635,7691,7765,8138,8224,8308,8411,8493,8576,8666,8733,8799,8872,8950,9038,9109,9186,9260,9332,9423,9497,9592,9690,9764,9844,9945,9998,10064,10153,10243,10305,10369,10432,10544,10657,10767,10879", "endLines": "6,37,40,41,42,47,48,65,68,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "321,3526,3825,3946,4030,4546,4641,6778,6993,7077,7143,7201,7274,7337,7393,7512,7569,7630,7686,7760,7905,8219,8303,8406,8488,8571,8661,8728,8794,8867,8945,9033,9104,9181,9255,9327,9418,9492,9587,9685,9759,9839,9940,9993,10059,10148,10238,10300,10364,10427,10539,10652,10762,10874,10953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,232,323,402,472,543,627,695,771,855,940,1030,1099,1183,1273,1348,1430,1509,1587,1665,1739,1824,1897,1973", "endColumns": "69,106,90,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "120,227,318,397,467,538,622,690,766,850,935,1025,1094,1178,1268,1343,1425,1504,1582,1660,1734,1819,1892,1968,2053"}, "to": {"startLines": "34,38,39,43,64,66,67,81,82,83,118,119,120,122,124,125,126,127,128,129,130,131,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3072,3531,3638,4035,6639,6783,6854,7910,7978,8054,10958,11043,11133,11332,11500,11590,11665,11747,11826,11904,11982,12056,12242,12315,12391", "endColumns": "69,106,90,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "3137,3633,3724,4109,6704,6849,6933,7973,8049,8133,11038,11128,11197,11411,11585,11660,11742,11821,11899,11977,12051,12136,12310,12386,12471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,447,551,664,748,852,973,1058,1138,1229,1322,1417,1511,1611,1704,1799,1893,1984,2076,2159,2271,2379,2479,2593,2699,2805,2969,11416", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "442,546,659,743,847,968,1053,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2071,2154,2266,2374,2474,2588,2694,2800,2964,3067,11495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,357,467,633,728,882,998,1112,1230,1371,1505,1640,1775,1924,2028,2188,2310,2451,2590,2721,2851,2960,3107,3197,3337,3436,3573", "endColumns": "169,131,109,165,94,153,115,113,117,140,133,134,134,148,103,159,121,140,138,130,129,108,146,89,139,98,136,108", "endOffsets": "220,352,462,628,723,877,993,1107,1225,1366,1500,1635,1770,1919,2023,2183,2305,2446,2585,2716,2846,2955,3102,3192,3332,3431,3568,3677"}, "to": {"startLines": "35,36,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,121,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3142,3312,4114,4224,4390,4646,4800,4916,5030,5148,5289,5423,5558,5693,5842,5946,6106,6228,6369,6508,11202,12476,12585,12732,12822,12962,13061,13198", "endColumns": "169,131,109,165,94,153,115,113,117,140,133,134,134,148,103,159,121,140,138,130,129,108,146,89,139,98,136,108", "endOffsets": "3307,3439,4219,4385,4480,4795,4911,5025,5143,5284,5418,5553,5688,5837,5941,6101,6223,6364,6503,6634,11327,12580,12727,12817,12957,13056,13193,13302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "12141", "endColumns": "100", "endOffsets": "12237"}}]}]}