import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, TextInput } from 'react-native';
import { pinService, UserData } from '../services/pinService';

interface PinLoginScreenProps {
  onPinLoginSuccess: (userData: UserData) => void;
  onUseEmailPassword: () => void;
}

export const PinLoginScreen: React.FC<PinLoginScreenProps> = ({
  onPinLoginSuccess,
  onUseEmailPassword,
}) => {
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [attempts, setAttempts] = useState(0);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    const data = await pinService.getUserData();
    setUserData(data);
  };

  const handlePinLogin = async () => {
    if (!pin || pin.length < 4) {
      Alert.alert('Error', 'Please enter your PIN');
      return;
    }

    setIsLoading(true);
    
    try {
      const verifiedUserData = await pinService.verifyPin(pin);
      
      if (verifiedUserData) {
        console.log('✅ PIN login successful');
        Alert.alert('Welcome Back!', `Hello ${verifiedUserData.email}`, [
          { text: 'OK', onPress: () => onPinLoginSuccess(verifiedUserData) }
        ]);
      } else {
        const newAttempts = attempts + 1;
        setAttempts(newAttempts);
        
        if (newAttempts >= 3) {
          Alert.alert(
            'Too Many Attempts',
            'Please use email and password to login.',
            [{ text: 'OK', onPress: onUseEmailPassword }]
          );
        } else {
          Alert.alert('Incorrect PIN', `Please try again. ${3 - newAttempts} attempts remaining.`);
          setPin('');
        }
      }
    } catch (error) {
      console.error('❌ PIN login error:', error);
      Alert.alert('Error', 'Failed to verify PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearPin = () => {
    Alert.alert(
      'Clear PIN',
      'Are you sure you want to remove your saved PIN? You will need to use email and password to login.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear PIN',
          style: 'destructive',
          onPress: async () => {
            await pinService.clearPin();
            onUseEmailPassword();
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔐 Enter PIN</Text>
      <Text style={styles.subtitle}>
        Welcome back! Enter your PIN to continue
      </Text>
      
      <View style={styles.content}>
        {userData && (
          <Text style={styles.userText}>
            Logged in as: {userData.email}
          </Text>
        )}
        
        <Text style={styles.infoText}>
          Enter your 4-digit PIN to quickly access AutoFlow
        </Text>
        
        <TextInput
          style={styles.input}
          placeholder="Enter PIN"
          value={pin}
          onChangeText={setPin}
          keyboardType="numeric"
          secureTextEntry
          maxLength={6}
          autoFocus
        />
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={handlePinLogin}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Verifying...' : 'Login with PIN'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.secondaryButton} 
          onPress={onUseEmailPassword}
          disabled={isLoading}
        >
          <Text style={styles.secondaryButtonText}>Use Email & Password</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.clearButton} 
          onPress={handleClearPin}
          disabled={isLoading}
        >
          <Text style={styles.clearButtonText}>Clear Saved PIN</Text>
        </TouchableOpacity>
      </View>
      
      <Text style={styles.footer}>
        🔒 Your PIN is stored securely on this device{'\n'}
        📱 Quick and secure access to AutoFlow
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#14b8a6',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  content: {
    alignItems: 'center',
    marginBottom: 40,
  },
  userText: {
    fontSize: 14,
    marginBottom: 15,
    color: '#666',
    textAlign: 'center',
  },
  infoText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 25,
    color: '#333',
    lineHeight: 22,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 15,
    fontSize: 18,
    backgroundColor: 'white',
    width: '100%',
    maxWidth: 200,
    textAlign: 'center',
    color: '#111827', // Dark gray text
    letterSpacing: 8,
  },
  button: {
    backgroundColor: '#14b8a6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginTop: 20,
    minWidth: 200,
  },
  buttonDisabled: {
    backgroundColor: '#94a3b8',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  secondaryButton: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    marginTop: 15,
  },
  secondaryButtonText: {
    color: '#14b8a6',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
  },
  clearButton: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    marginTop: 20,
  },
  clearButtonText: {
    color: '#ef4444',
    fontSize: 14,
    textAlign: 'center',
  },
  footer: {
    fontSize: 14,
    textAlign: 'center',
    color: '#10b981',
    lineHeight: 20,
  },
});
