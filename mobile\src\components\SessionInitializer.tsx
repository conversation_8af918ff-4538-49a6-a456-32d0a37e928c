import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { useAuth } from '@/store/auth';
import { theme } from '@/theme/theme';

interface SessionInitializerProps {
  children: React.ReactNode;
}

export const SessionInitializer: React.FC<SessionInitializerProps> = ({ children }) => {
  const { isLoading, initializeSession } = useAuth();

  useEffect(() => {
    initializeSession();
  }, [initializeSession]);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading AutoFlow...</Text>
      </View>
    );
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.onSurface,
  },
});
