{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "109", "startColumns": "4", "startOffsets": "10354", "endColumns": "100", "endOffsets": "10450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,339,453,618,713,866,981,1107,1230,1386,1523,1676,1808,1952,2046,2219,2354,2504,2650,2772,2902,3003,3142,3231,3364,3473,3620", "endColumns": "157,125,113,164,94,152,114,125,122,155,136,152,131,143,93,172,134,149,145,121,129,100,138,88,132,108,146,108", "endOffsets": "208,334,448,613,708,861,976,1102,1225,1381,1518,1671,1803,1947,2041,2214,2349,2499,2645,2767,2897,2998,3137,3226,3359,3468,3615,3724"}, "to": {"startLines": "33,34,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,107,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2962,3120,3642,3756,3921,4186,4339,4454,4580,4703,4859,4996,5149,5281,5425,5519,5692,5827,5977,6123,10142,10455,10556,10695,10784,10917,11026,11173", "endColumns": "157,125,113,164,94,152,114,125,122,155,136,152,131,143,93,172,134,149,145,121,129,100,138,88,132,108,146,108", "endOffsets": "3115,3241,3751,3916,4011,4334,4449,4575,4698,4854,4991,5144,5276,5420,5514,5687,5822,5972,6118,6240,10267,10551,10690,10779,10912,11021,11168,11277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,384,491,603,691,794,909,988,1065,1156,1249,1344,1438,1538,1631,1726,1820,1911,2004,2085,2189,2292,2390,2497,2604,2709,2866,10272", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "379,486,598,686,789,904,983,1060,1151,1244,1339,1433,1533,1626,1721,1815,1906,1999,2080,2184,2287,2385,2492,2599,2704,2861,2957,10349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,303,415,541,622,689,792,867,930,1022,1087,1154,1226,1298,1352,1473,1532,1596,1650,1727,1859,1944,2025,2144,2231,2314,2406,2473,2539,2611,2688,2779,2859,2938,3013,3092,3182,3255,3349,3446,3520,3593,3692,3747,3815,3903,3992,4054,4118,4181,4290,4395,4498,4607", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,118,86,82,91,66,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,81", "endOffsets": "221,298,410,536,617,684,787,862,925,1017,1082,1149,1221,1293,1347,1468,1527,1591,1645,1722,1854,1939,2020,2139,2226,2309,2401,2468,2534,2606,2683,2774,2854,2933,3008,3087,3177,3250,3344,3441,3515,3588,3687,3742,3810,3898,3987,4049,4113,4176,4285,4390,4493,4602,4684"}, "to": {"startLines": "2,35,36,37,38,42,43,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3246,3323,3435,3561,4016,4083,6245,6320,6383,6475,6540,6607,6679,6751,6805,6926,6985,7049,7103,7180,7312,7397,7478,7597,7684,7767,7859,7926,7992,8064,8141,8232,8312,8391,8466,8545,8635,8708,8802,8899,8973,9046,9145,9200,9268,9356,9445,9507,9571,9634,9743,9848,9951,10060", "endLines": "5,35,36,37,38,42,43,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,118,86,82,91,66,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,81", "endOffsets": "271,3318,3430,3556,3637,4078,4181,6315,6378,6470,6535,6602,6674,6746,6800,6921,6980,7044,7098,7175,7307,7392,7473,7592,7679,7762,7854,7921,7987,8059,8136,8227,8307,8386,8461,8540,8630,8703,8797,8894,8968,9041,9140,9195,9263,9351,9440,9502,9566,9629,9738,9843,9946,10055,10137"}}]}]}