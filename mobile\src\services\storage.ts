// Real storage service using AsyncStorage
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys for consistency
const STORAGE_KEYS = {
  USER_LOGGED_IN: 'user_logged_in',
  USER_EMAIL: 'user_email',
  USER_TOKEN: 'user_token',
  USER_PROFILE: 'user_profile',
  BIOMETRIC_ENABLED: 'biometric_enabled',
  PIN_ENABLED: 'pin_enabled',
  PIN_HASH: 'pin_hash',
  LAST_LOGIN: 'last_login',
};

class SimpleStorageService {
  constructor() {
    console.log('Storage: Initialized AsyncStorage service');
  }

  // Set item in storage
  async setItem(key: string, value: string): Promise<void> {
    try {
      console.log(`Storage: Setting ${key} = ${value}`);
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      console.error(`Storage: Error setting ${key}:`, error);
      throw error;
    }
  }

  // Get item from storage
  async getItem(key: string): Promise<string | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      console.log(`Storage: Retrieved ${key} = ${value}`);
      return value;
    } catch (error) {
      console.error(`Storage: Error getting ${key}:`, error);
      return null;
    }
  }

  // Remove item from storage
  async removeItem(key: string): Promise<void> {
    try {
      console.log(`Storage: Removing ${key}`);
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error(`Storage: Error removing ${key}:`, error);
      throw error;
    }
  }

  // Clear all storage
  async clear(): Promise<void> {
    try {
      console.log('Storage: Clearing all storage');
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Storage: Error clearing storage:', error);
      throw error;
    }
  }

  // Get all keys
  async getAllKeys(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      console.log('Storage: All keys:', keys);
      return keys;
    } catch (error) {
      console.error('Storage: Error getting all keys:', error);
      return [];
    }
  }

  // Check if storage contains key
  async hasKey(key: string): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem(key);
      const hasKey = value !== null;
      console.log(`Storage: Has key ${key} = ${hasKey}`);
      return hasKey;
    } catch (error) {
      console.error(`Storage: Error checking key ${key}:`, error);
      return false;
    }
  }

  // Get multiple items
  async multiGet(keys: string[]): Promise<Array<[string, string | null]>> {
    try {
      console.log('Storage: Getting multiple keys:', keys);
      const results = await AsyncStorage.multiGet(keys);
      return results;
    } catch (error) {
      console.error('Storage: Error getting multiple items:', error);
      return keys.map(key => [key, null]);
    }
  }

  // Set multiple items
  async multiSet(keyValuePairs: Array<[string, string]>): Promise<void> {
    try {
      console.log('Storage: Setting multiple items:', keyValuePairs);
      await AsyncStorage.multiSet(keyValuePairs);
    } catch (error) {
      console.error('Storage: Error setting multiple items:', error);
      throw error;
    }
  }

  // Remove multiple items
  async multiRemove(keys: string[]): Promise<void> {
    try {
      console.log('Storage: Removing multiple keys:', keys);
      await AsyncStorage.multiRemove(keys);
    } catch (error) {
      console.error('Storage: Error removing multiple items:', error);
      throw error;
    }
  }

  // Helper methods for common storage operations
  async setUserData(userData: {
    email: string;
    token?: string;
    profile?: any;
    isLoggedIn: boolean;
  }): Promise<void> {
    const pairs: Array<[string, string]> = [
      [STORAGE_KEYS.USER_EMAIL, userData.email],
      [STORAGE_KEYS.USER_LOGGED_IN, userData.isLoggedIn.toString()],
    ];

    if (userData.token) {
      pairs.push([STORAGE_KEYS.USER_TOKEN, userData.token]);
    }

    if (userData.profile) {
      pairs.push([STORAGE_KEYS.USER_PROFILE, JSON.stringify(userData.profile)]);
    }

    await this.multiSet(pairs);
  }

  async getUserData(): Promise<{
    email: string | null;
    token: string | null;
    profile: any | null;
    isLoggedIn: boolean;
  }> {
    const keys = [
      STORAGE_KEYS.USER_EMAIL,
      STORAGE_KEYS.USER_TOKEN,
      STORAGE_KEYS.USER_PROFILE,
      STORAGE_KEYS.USER_LOGGED_IN,
    ];

    const results = await this.multiGet(keys);
    const data = Object.fromEntries(results);

    return {
      email: data[STORAGE_KEYS.USER_EMAIL],
      token: data[STORAGE_KEYS.USER_TOKEN],
      profile: data[STORAGE_KEYS.USER_PROFILE] ? JSON.parse(data[STORAGE_KEYS.USER_PROFILE]) : null,
      isLoggedIn: data[STORAGE_KEYS.USER_LOGGED_IN] === 'true',
    };
  }

  async clearUserData(): Promise<void> {
    const keys = [
      STORAGE_KEYS.USER_EMAIL,
      STORAGE_KEYS.USER_TOKEN,
      STORAGE_KEYS.USER_PROFILE,
      STORAGE_KEYS.USER_LOGGED_IN,
    ];

    await this.multiRemove(keys);
  }
}

export const simpleStorage = new SimpleStorageService();
export default simpleStorage;
