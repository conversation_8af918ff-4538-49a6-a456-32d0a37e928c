{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "133", "startColumns": "4", "startOffsets": "12024", "endColumns": "100", "endOffsets": "12120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "372,484,586,694,781,884,1003,1084,1162,1254,1348,1443,1537,1632,1726,1822,1922,2014,2106,2190,2298,2406,2506,2619,2727,2832,3012,11302", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "479,581,689,776,879,998,1079,1157,1249,1343,1438,1532,1627,1721,1817,1917,2009,2101,2185,2293,2401,2501,2614,2722,2827,3007,3107,11381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,322,410,516,642,726,792,886,962,1025,1137,1202,1256,1326,1386,1442,1554,1611,1673,1729,1802,1936,2021,2106,2219,2303,2386,2475,2542,2608,2681,2758,2842,2916,2992,3067,3140,3228,3301,3391,3482,3554,3628,3719,3771,3838,3922,4009,4071,4135,4198,4301,4398,4496,4593", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,87,105,125,83,65,93,75,62,111,64,53,69,59,55,111,56,61,55,72,133,84,84,112,83,82,88,66,65,72,76,83,73,75,74,72,87,72,89,90,71,73,90,51,66,83,86,61,63,62,102,96,97,96,76", "endOffsets": "317,405,511,637,721,787,881,957,1020,1132,1197,1251,1321,1381,1437,1549,1606,1668,1724,1797,1931,2016,2101,2214,2298,2381,2470,2537,2603,2676,2753,2837,2911,2987,3062,3135,3223,3296,3386,3477,3549,3623,3714,3766,3833,3917,4004,4066,4130,4193,4296,4393,4491,4588,4665"}, "to": {"startLines": "2,38,40,41,42,47,48,65,68,70,71,72,73,74,75,76,77,78,79,80,81,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3494,3668,3774,3900,4420,4486,6626,6852,6986,7098,7163,7217,7287,7347,7403,7515,7572,7634,7690,7763,8117,8202,8287,8400,8484,8567,8656,8723,8789,8862,8939,9023,9097,9173,9248,9321,9409,9482,9572,9663,9735,9809,9900,9952,10019,10103,10190,10252,10316,10379,10482,10579,10677,10774", "endLines": "7,38,40,41,42,47,48,65,68,70,71,72,73,74,75,76,77,78,79,80,81,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "endColumns": "12,87,105,125,83,65,93,75,62,111,64,53,69,59,55,111,56,61,55,72,133,84,84,112,83,82,88,66,65,72,76,83,73,75,74,72,87,72,89,90,71,73,90,51,66,83,86,61,63,62,102,96,97,96,76", "endOffsets": "367,3577,3769,3895,3979,4481,4575,6697,6910,7093,7158,7212,7282,7342,7398,7510,7567,7629,7685,7758,7892,8197,8282,8395,8479,8562,8651,8718,8784,8857,8934,9018,9092,9168,9243,9316,9404,9477,9567,9658,9730,9804,9895,9947,10014,10098,10185,10247,10311,10374,10477,10574,10672,10769,10846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,214,298,369,438,519,590,657,727,810,893,975,1047,1121,1203,1280,1362,1444,1520,1598,1675,1759,1833,1915,1987", "endColumns": "72,85,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "123,209,293,364,433,514,585,652,722,805,888,970,1042,1116,1198,1275,1357,1439,1515,1593,1670,1754,1828,1910,1982,2064"}, "to": {"startLines": "35,39,43,64,66,67,69,82,83,84,119,120,121,123,125,126,127,128,129,130,131,132,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3112,3582,3984,6555,6702,6771,6915,7897,7964,8034,10851,10934,11016,11228,11386,11468,11545,11627,11709,11785,11863,11940,12125,12199,12281,12353", "endColumns": "72,85,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "3180,3663,4063,6621,6766,6847,6981,7959,8029,8112,10929,11011,11083,11297,11463,11540,11622,11704,11780,11858,11935,12019,12194,12276,12348,12430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,364,471,625,716,876,987,1103,1224,1362,1492,1618,1743,1883,1982,2156,2281,2424,2558,2691,2831,2928,3074,3161,3297,3398,3548", "endColumns": "178,129,106,153,90,159,110,115,120,137,129,125,124,139,98,173,124,142,133,132,139,96,145,86,135,100,149,117", "endOffsets": "229,359,466,620,711,871,982,1098,1219,1357,1487,1613,1738,1878,1977,2151,2276,2419,2553,2686,2826,2923,3069,3156,3292,3393,3543,3661"}, "to": {"startLines": "36,37,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,122,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3185,3364,4068,4175,4329,4580,4740,4851,4967,5088,5226,5356,5482,5607,5747,5846,6020,6145,6288,6422,11088,12435,12532,12678,12765,12901,13002,13152", "endColumns": "178,129,106,153,90,159,110,115,120,137,129,125,124,139,98,173,124,142,133,132,139,96,145,86,135,100,149,117", "endOffsets": "3359,3489,4170,4324,4415,4735,4846,4962,5083,5221,5351,5477,5602,5742,5841,6015,6140,6283,6417,6550,11223,12527,12673,12760,12896,12997,13147,13265"}}]}]}