// app/api/auth/biometric-delete/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'userId is required' },
        { status: 400 }
      );
    }

    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response });

    // Verify the user exists
    const { data: user, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError || !user) {
      console.error('Biometric delete - user not found:', userError);
      return NextResponse.json(
        { success: false },
        { status: 401 }
      );
    }

    // Delete the biometric key from the database
    const { error: deleteError } = await supabase
      .from('biometric_keys')
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Biometric delete - error:', deleteError);
      return NextResponse.json(
        { success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
    });

  } catch (error) {
    console.error('Biometric deletion error:', error);
    return NextResponse.json(
      { success: false },
      { status: 500 }
    );
  }
}
