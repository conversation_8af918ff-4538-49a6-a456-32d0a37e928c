{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,124,207,277,344,416", "endColumns": "68,82,69,66,71,71", "endOffsets": "119,202,272,339,411,483"}, "to": {"startLines": "60,61,63,76,113,114", "startColumns": "4,4,4,4,4,4", "startOffsets": "6286,6355,6499,7455,10484,10556", "endColumns": "68,82,69,66,71,71", "endOffsets": "6350,6433,6564,7517,10551,10623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,381,490,602,687,792,909,988,1066,1157,1250,1345,1439,1539,1632,1727,1822,1913,2004,2085,2191,2296,2394,2501,2604,2719,2880,10402", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "376,485,597,682,787,904,983,1061,1152,1245,1340,1434,1534,1627,1722,1817,1908,1999,2080,2186,2291,2389,2496,2599,2714,2875,2977,10479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "10628", "endColumns": "100", "endOffsets": "10724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,351,460,615,705,853,965,1082,1202,1349,1478,1628,1753,1906,2005,2161,2290,2432,2591,2720,2847,2962,3108,3212,3352,3452,3588", "endColumns": "167,127,108,154,89,147,111,116,119,146,128,149,124,152,98,155,128,141,158,128,126,114,145,103,139,99,135,103", "endOffsets": "218,346,455,610,700,848,960,1077,1197,1344,1473,1623,1748,1901,2000,2156,2285,2427,2586,2715,2842,2957,3103,3207,3347,3447,3583,3687"}, "to": {"startLines": "33,34,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,111,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2982,3150,3689,3798,3953,4201,4349,4461,4578,4698,4845,4974,5124,5249,5402,5501,5657,5786,5928,6087,10275,10729,10844,10990,11094,11234,11334,11470", "endColumns": "167,127,108,154,89,147,111,116,119,146,128,149,124,152,98,155,128,141,158,128,126,114,145,103,139,99,135,103", "endOffsets": "3145,3273,3793,3948,4038,4344,4456,4573,4693,4840,4969,5119,5244,5397,5496,5652,5781,5923,6082,6211,10397,10839,10985,11089,11229,11329,11465,11569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,305,406,547,631,695,789,859,920,1007,1071,1130,1204,1266,1320,1437,1495,1556,1610,1684,1806,1890,1986,2088,2166,2244,2333,2400,2466,2535,2612,2699,2771,2847,2929,3002,3087,3166,3256,3348,3422,3507,3597,3649,3714,3797,3882,3944,4008,4071,4188,4282,4382,4477", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,100,140,83,63,93,69,60,86,63,58,73,61,53,116,57,60,53,73,121,83,95,101,77,77,88,66,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,82,84,61,63,62,116,93,99,94,81", "endOffsets": "215,300,401,542,626,690,784,854,915,1002,1066,1125,1199,1261,1315,1432,1490,1551,1605,1679,1801,1885,1981,2083,2161,2239,2328,2395,2461,2530,2607,2694,2766,2842,2924,2997,3082,3161,3251,3343,3417,3502,3592,3644,3709,3792,3877,3939,4003,4066,4183,4277,4377,4472,4554"}, "to": {"startLines": "2,35,36,37,38,42,43,59,62,64,65,66,67,68,69,70,71,72,73,74,75,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3278,3363,3464,3605,4043,4107,6216,6438,6569,6656,6720,6779,6853,6915,6969,7086,7144,7205,7259,7333,7522,7606,7702,7804,7882,7960,8049,8116,8182,8251,8328,8415,8487,8563,8645,8718,8803,8882,8972,9064,9138,9223,9313,9365,9430,9513,9598,9660,9724,9787,9904,9998,10098,10193", "endLines": "5,35,36,37,38,42,43,59,62,64,65,66,67,68,69,70,71,72,73,74,75,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "12,84,100,140,83,63,93,69,60,86,63,58,73,61,53,116,57,60,53,73,121,83,95,101,77,77,88,66,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,82,84,61,63,62,116,93,99,94,81", "endOffsets": "265,3358,3459,3600,3684,4102,4196,6281,6494,6651,6715,6774,6848,6910,6964,7081,7139,7200,7254,7328,7450,7601,7697,7799,7877,7955,8044,8111,8177,8246,8323,8410,8482,8558,8640,8713,8798,8877,8967,9059,9133,9218,9308,9360,9425,9508,9593,9655,9719,9782,9899,9993,10093,10188,10270"}}]}]}