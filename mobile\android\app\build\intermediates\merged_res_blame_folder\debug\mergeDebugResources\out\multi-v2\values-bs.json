{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,329,438,594,687,848,969,1085,1208,1340,1471,1599,1729,1863,1964,2125,2246,2381,2514,2637,2764,2861,2998,3099,3240,3341,3482", "endColumns": "156,116,108,155,92,160,120,115,122,131,130,127,129,133,100,160,120,134,132,122,126,96,136,100,140,100,140,108", "endOffsets": "207,324,433,589,682,843,964,1080,1203,1335,1466,1594,1724,1858,1959,2120,2241,2376,2509,2632,2759,2856,2993,3094,3235,3336,3477,3586"}, "to": {"startLines": "35,36,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,121,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3131,3288,3964,4073,4229,4480,4641,4762,4878,5001,5133,5264,5392,5522,5656,5757,5918,6039,6174,6307,11019,12363,12460,12597,12698,12839,12940,13081", "endColumns": "156,116,108,155,92,160,120,115,122,131,130,127,129,133,100,160,120,134,132,122,126,96,136,100,140,100,140,108", "endOffsets": "3283,3400,4068,4224,4317,4636,4757,4873,4996,5128,5259,5387,5517,5651,5752,5913,6034,6169,6302,6425,11141,12455,12592,12693,12834,12935,13076,13185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,218,303,377,446,528,595,662,740,820,904,987,1059,1140,1227,1303,1386,1468,1547,1625,1701,1789,1862,1941,2011", "endColumns": "73,88,84,73,68,81,66,66,77,79,83,82,71,80,86,75,82,81,78,77,75,87,72,78,69,76", "endOffsets": "124,213,298,372,441,523,590,657,735,815,899,982,1054,1135,1222,1298,1381,1463,1542,1620,1696,1784,1857,1936,2006,2083"}, "to": {"startLines": "34,38,42,63,65,66,68,81,82,83,118,119,120,122,124,125,126,127,128,129,130,131,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3057,3487,3879,6430,6581,6650,6795,7768,7835,7913,10780,10864,10947,11146,11314,11401,11477,11560,11642,11721,11799,11875,12064,12137,12216,12286", "endColumns": "73,88,84,73,68,81,66,66,77,79,83,82,71,80,86,75,82,81,78,77,75,87,72,78,69,76", "endOffsets": "3126,3571,3959,6499,6645,6727,6857,7830,7908,7988,10859,10942,11014,11222,11396,11472,11555,11637,11716,11794,11870,11958,12132,12211,12281,12358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "332,453,550,657,743,847,969,1054,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2162,2265,2369,2470,2575,2689,2792,2961,11227", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "448,545,652,738,842,964,1049,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2157,2260,2364,2465,2570,2684,2787,2956,3052,11309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "11963", "endColumns": "100", "endOffsets": "12059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,364,460,586,667,733,825,902,965,1073,1139,1195,1266,1326,1380,1499,1556,1618,1672,1747,1871,1959,2042,2157,2242,2328,2416,2483,2549,2623,2701,2788,2860,2937,3010,3080,3173,3245,3337,3433,3507,3583,3679,3732,3799,3886,3973,4035,4099,4162,4270,4372,4473,4578", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,114,84,85,87,66,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,79", "endOffsets": "277,359,455,581,662,728,820,897,960,1068,1134,1190,1261,1321,1375,1494,1551,1613,1667,1742,1866,1954,2037,2152,2237,2323,2411,2478,2544,2618,2696,2783,2855,2932,3005,3075,3168,3240,3332,3428,3502,3578,3674,3727,3794,3881,3968,4030,4094,4157,4265,4367,4468,4573,4653"}, "to": {"startLines": "2,37,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3405,3576,3672,3798,4322,4388,6504,6732,6862,6970,7036,7092,7163,7223,7277,7396,7453,7515,7569,7644,7993,8081,8164,8279,8364,8450,8538,8605,8671,8745,8823,8910,8982,9059,9132,9202,9295,9367,9459,9555,9629,9705,9801,9854,9921,10008,10095,10157,10221,10284,10392,10494,10595,10700", "endLines": "6,37,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,114,84,85,87,66,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,79", "endOffsets": "327,3482,3667,3793,3874,4383,4475,6576,6790,6965,7031,7087,7158,7218,7272,7391,7448,7510,7564,7639,7763,8076,8159,8274,8359,8445,8533,8600,8666,8740,8818,8905,8977,9054,9127,9197,9290,9362,9454,9550,9624,9700,9796,9849,9916,10003,10090,10152,10216,10279,10387,10489,10590,10695,10775"}}]}]}