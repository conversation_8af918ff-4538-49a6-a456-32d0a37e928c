// app/api/auth/biometric-challenge/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';
import { randomBytes } from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'userId is required' },
        { status: 400 }
      );
    }

    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response });

    // Verify the user exists
    const { data: user, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError || !user) {
      console.error('Biometric challenge - user not found:', userError);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 401 }
      );
    }

    // Check if user has a biometric key registered
    const { data: biometricKey, error: keyError } = await supabase
      .from('biometric_keys')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (keyError || !biometricKey) {
      console.error('Biometric challenge - no key found:', keyError);
      return NextResponse.json(
        { error: 'No biometric key registered' },
        { status: 404 }
      );
    }

    // Generate a random challenge
    const challenge = randomBytes(32).toString('base64');

    // In a real implementation, you might want to store the challenge temporarily
    // with an expiration time to prevent replay attacks

    return NextResponse.json({
      challenge,
      expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes
    });

  } catch (error) {
    console.error('Biometric challenge error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
