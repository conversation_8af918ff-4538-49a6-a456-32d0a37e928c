{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,303,409,525,608,673,767,832,891,978,1040,1100,1166,1228,1282,1394,1451,1512,1566,1638,1764,1850,1934,2043,2124,2205,2295,2362,2428,2500,2584,2667,2742,2818,2891,2966,3051,3126,3218,3312,3386,3459,3553,3605,3674,3759,3846,3908,3972,4035,4138,4238,4333,4435", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "219,298,404,520,603,668,762,827,886,973,1035,1095,1161,1223,1277,1389,1446,1507,1561,1633,1759,1845,1929,2038,2119,2200,2290,2357,2423,2495,2579,2662,2737,2813,2886,2961,3046,3121,3213,3307,3381,3454,3548,3600,3669,3754,3841,3903,3967,4030,4133,4233,4328,4430,4510"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3322,3594,3700,3816,4328,4393,6475,6694,6823,6910,6972,7032,7098,7160,7214,7326,7383,7444,7498,7570,7848,7934,8018,8127,8208,8289,8379,8446,8512,8584,8668,8751,8826,8902,8975,9050,9135,9210,9302,9396,9470,9543,9637,9689,9758,9843,9930,9992,10056,10119,10222,10322,10417,10519", "endLines": "5,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "269,3396,3695,3811,3894,4388,4482,6535,6748,6905,6967,7027,7093,7155,7209,7321,7378,7439,7493,7565,7691,7929,8013,8122,8203,8284,8374,8441,8507,8579,8663,8746,8821,8897,8970,9045,9130,9205,9297,9391,9465,9538,9632,9684,9753,9838,9925,9987,10051,10114,10217,10317,10412,10514,10594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,231,322,400,470,540,624,694,770,846,929,1008,1087,1168,1242,1325,1407,1485,1561,1633,1721,1796,1872,1950", "endColumns": "73,101,90,77,69,69,83,69,75,75,82,78,78,80,73,82,81,77,75,71,87,74,75,77,76", "endOffsets": "124,226,317,395,465,535,619,689,765,841,924,1003,1082,1163,1237,1320,1402,1480,1556,1628,1716,1791,1867,1945,2022"}, "to": {"startLines": "33,37,38,42,63,65,66,68,81,82,117,118,120,122,123,124,125,126,127,128,129,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2978,3401,3503,3899,6405,6540,6610,6753,7696,7772,10599,10682,10885,11049,11130,11204,11287,11369,11447,11523,11595,11784,11859,11935,12013", "endColumns": "73,101,90,77,69,69,83,69,75,75,82,78,78,80,73,82,81,77,75,71,87,74,75,77,76", "endOffsets": "3047,3498,3589,3972,6470,6605,6689,6818,7767,7843,10677,10756,10959,11125,11199,11282,11364,11442,11518,11590,11678,11854,11930,12008,12085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,325,432,578,676,818,928,1038,1153,1282,1420,1554,1683,1818,1918,2072,2194,2333,2470,2594,2718,2814,2942,3029,3148,3247,3378", "endColumns": "150,118,106,145,97,141,109,109,114,128,137,133,128,134,99,153,121,138,136,123,123,95,127,86,118,98,130,99", "endOffsets": "201,320,427,573,671,813,923,1033,1148,1277,1415,1549,1678,1813,1913,2067,2189,2328,2465,2589,2713,2809,2937,3024,3143,3242,3373,3473"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,119,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3052,3203,3977,4084,4230,4487,4629,4739,4849,4964,5093,5231,5365,5494,5629,5729,5883,6005,6144,6281,10761,12090,12186,12314,12401,12520,12619,12750", "endColumns": "150,118,106,145,97,141,109,109,114,128,137,133,128,134,99,153,121,138,136,123,123,95,127,86,118,98,130,99", "endOffsets": "3198,3317,4079,4225,4323,4624,4734,4844,4959,5088,5226,5360,5489,5624,5724,5878,6000,6139,6276,6400,10880,12181,12309,12396,12515,12614,12745,12845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,389,493,598,685,789,905,988,1066,1157,1250,1345,1439,1539,1632,1727,1821,1912,2003,2089,2192,2297,2398,2502,2611,2719,2879,10964", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "384,488,593,680,784,900,983,1061,1152,1245,1340,1434,1534,1627,1722,1816,1907,1998,2084,2187,2292,2393,2497,2606,2714,2874,2973,11044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "130", "startColumns": "4", "startOffsets": "11683", "endColumns": "100", "endOffsets": "11779"}}]}]}