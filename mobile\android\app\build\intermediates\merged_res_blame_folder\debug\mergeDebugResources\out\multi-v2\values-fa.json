{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,374,475,586,670,771,886,966,1043,1136,1231,1323,1417,1519,1614,1711,1805,1898,1988,2070,2178,2282,2380,2486,2591,2696,2853,10983", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "369,470,581,665,766,881,961,1038,1131,1226,1318,1412,1514,1609,1706,1800,1893,1983,2065,2173,2277,2375,2481,2586,2691,2848,2949,11060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,296,393,522,606,669,759,828,888,979,1043,1102,1169,1231,1286,1409,1467,1528,1583,1655,1792,1873,1955,2055,2129,2203,2289,2356,2422,2493,2570,2651,2724,2798,2868,2942,3028,3102,3191,3283,3357,3430,3519,3570,3637,3720,3804,3866,3930,3993,4087,4194,4287,4392", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,96,128,83,62,89,68,59,90,63,58,66,61,54,122,57,60,54,71,136,80,81,99,73,73,85,66,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,77", "endOffsets": "209,291,388,517,601,664,754,823,883,974,1038,1097,1164,1226,1281,1404,1462,1523,1578,1650,1787,1868,1950,2050,2124,2198,2284,2351,2417,2488,2565,2646,2719,2793,2863,2937,3023,3097,3186,3278,3352,3425,3514,3565,3632,3715,3799,3861,3925,3988,4082,4189,4282,4387,4465"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3294,3561,3658,3787,4300,4363,6406,6625,6753,6844,6908,6967,7034,7096,7151,7274,7332,7393,7448,7520,7872,7953,8035,8135,8209,8283,8369,8436,8502,8573,8650,8731,8804,8878,8948,9022,9108,9182,9271,9363,9437,9510,9599,9650,9717,9800,9884,9946,10010,10073,10167,10274,10367,10472", "endLines": "5,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "12,81,96,128,83,62,89,68,59,90,63,58,66,61,54,122,57,60,54,71,136,80,81,99,73,73,85,66,65,70,76,80,72,73,69,73,85,73,88,91,73,72,88,50,66,82,83,61,63,62,93,106,92,104,77", "endOffsets": "259,3371,3653,3782,3866,4358,4448,6470,6680,6839,6903,6962,7029,7091,7146,7269,7327,7388,7443,7515,7652,7948,8030,8130,8204,8278,8364,8431,8497,8568,8645,8726,8799,8873,8943,9017,9103,9177,9266,9358,9432,9505,9594,9645,9712,9795,9879,9941,10005,10068,10162,10269,10362,10467,10545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "11691", "endColumns": "100", "endOffsets": "11787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,224,309,386,458,527,608,676,742,816,891,972,1053,1122,1201,1279,1353,1435,1516,1595,1668,1739,1827,1898,1974,2046", "endColumns": "68,99,84,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "119,219,304,381,453,522,603,671,737,811,886,967,1048,1117,1196,1274,1348,1430,1511,1590,1663,1734,1822,1893,1969,2041,2117"}, "to": {"startLines": "33,37,38,42,63,65,66,68,81,82,83,118,119,120,122,124,125,126,127,128,129,130,131,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2954,3376,3476,3871,6334,6475,6544,6685,7657,7723,7797,10550,10631,10712,10904,11065,11143,11217,11299,11380,11459,11532,11603,11792,11863,11939,12011", "endColumns": "68,99,84,76,71,68,80,67,65,73,74,80,80,68,78,77,73,81,80,78,72,70,87,70,75,71,75", "endOffsets": "3018,3471,3556,3943,6401,6539,6620,6748,7718,7792,7867,10626,10707,10776,10978,11138,11212,11294,11375,11454,11527,11598,11686,11858,11934,12006,12082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,326,438,590,678,822,937,1046,1163,1291,1414,1558,1675,1799,1896,2046,2172,2306,2444,2559,2682,2791,2927,3016,3132,3233,3361", "endColumns": "148,121,111,151,87,143,114,108,116,127,122,143,116,123,96,149,125,133,137,114,122,108,135,88,115,100,127,104", "endOffsets": "199,321,433,585,673,817,932,1041,1158,1286,1409,1553,1670,1794,1891,2041,2167,2301,2439,2554,2677,2786,2922,3011,3127,3228,3356,3461"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,121,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3023,3172,3948,4060,4212,4453,4597,4712,4821,4938,5066,5189,5333,5450,5574,5671,5821,5947,6081,6219,10781,12087,12196,12332,12421,12537,12638,12766", "endColumns": "148,121,111,151,87,143,114,108,116,127,122,143,116,123,96,149,125,133,137,114,122,108,135,88,115,100,127,104", "endOffsets": "3167,3289,4055,4207,4295,4592,4707,4816,4933,5061,5184,5328,5445,5569,5666,5816,5942,6076,6214,6329,10899,12191,12327,12416,12532,12633,12761,12866"}}]}]}