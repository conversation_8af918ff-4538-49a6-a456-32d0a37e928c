{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,433,534,640,726,830,952,1036,1117,1208,1301,1396,1490,1590,1683,1778,1883,1974,2065,2151,2256,2362,2465,2571,2680,2787,2957,11338", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "428,529,635,721,825,947,1031,1112,1203,1296,1391,1485,1585,1678,1773,1878,1969,2060,2146,2251,2357,2460,2566,2675,2782,2952,3049,11420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "133", "startColumns": "4", "startOffsets": "12067", "endColumns": "100", "endOffsets": "12163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,356,450,581,662,728,820,888,951,1054,1120,1176,1247,1307,1361,1473,1530,1591,1645,1721,1846,1932,2015,2123,2204,2287,2375,2442,2508,2582,2660,2749,2824,2900,2975,3046,3136,3209,3301,3397,3469,3545,3641,3694,3761,3848,3935,3997,4061,4124,4229,4333,4429,4536", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "271,351,445,576,657,723,815,883,946,1049,1115,1171,1242,1302,1356,1468,1525,1586,1640,1716,1841,1927,2010,2118,2199,2282,2370,2437,2503,2577,2655,2744,2819,2895,2970,3041,3131,3204,3296,3392,3464,3540,3636,3689,3756,3843,3930,3992,4056,4119,4224,4328,4424,4531,4611"}, "to": {"startLines": "2,37,40,41,42,47,48,65,68,70,71,72,73,74,75,76,77,78,79,80,81,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3432,3712,3806,3937,4457,4523,6630,6849,6979,7082,7148,7204,7275,7335,7389,7501,7558,7619,7673,7749,8106,8192,8275,8383,8464,8547,8635,8702,8768,8842,8920,9009,9084,9160,9235,9306,9396,9469,9561,9657,9729,9805,9901,9954,10021,10108,10195,10257,10321,10384,10489,10593,10689,10796", "endLines": "6,37,40,41,42,47,48,65,68,70,71,72,73,74,75,76,77,78,79,80,81,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "321,3507,3801,3932,4013,4518,4610,6693,6907,7077,7143,7199,7270,7330,7384,7496,7553,7614,7668,7744,7869,8187,8270,8378,8459,8542,8630,8697,8763,8837,8915,9004,9079,9155,9230,9301,9391,9464,9556,9652,9724,9800,9896,9949,10016,10103,10190,10252,10316,10379,10484,10588,10684,10791,10871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,239,329,412,485,554,636,703,770,852,935,1023,1106,1178,1263,1349,1425,1507,1589,1665,1742,1817,1905,1977,2056,2126", "endColumns": "73,109,89,82,72,68,81,66,66,81,82,87,82,71,84,85,75,81,81,75,76,74,87,71,78,69,82", "endOffsets": "124,234,324,407,480,549,631,698,765,847,930,1018,1101,1173,1258,1344,1420,1502,1584,1660,1737,1812,1900,1972,2051,2121,2204"}, "to": {"startLines": "34,38,39,43,64,66,67,69,82,83,84,119,120,121,123,125,126,127,128,129,130,131,132,134,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3054,3512,3622,4018,6557,6698,6767,6912,7874,7941,8023,10876,10964,11047,11253,11425,11511,11587,11669,11751,11827,11904,11979,12168,12240,12319,12389", "endColumns": "73,109,89,82,72,68,81,66,66,81,82,87,82,71,84,85,75,81,81,75,76,74,87,71,78,69,82", "endOffsets": "3123,3617,3707,4096,6625,6762,6844,6974,7936,8018,8101,10959,11042,11114,11333,11506,11582,11664,11746,11822,11899,11974,12062,12235,12314,12384,12467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,359,470,622,715,865,988,1101,1222,1356,1487,1614,1745,1879,1979,2144,2259,2398,2531,2657,2791,2890,3027,3115,3254,3357,3498", "endColumns": "170,132,110,151,92,149,122,112,120,133,130,126,130,133,99,164,114,138,132,125,133,98,136,87,138,102,140,106", "endOffsets": "221,354,465,617,710,860,983,1096,1217,1351,1482,1609,1740,1874,1974,2139,2254,2393,2526,2652,2786,2885,3022,3110,3249,3352,3493,3600"}, "to": {"startLines": "35,36,44,45,46,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,122,138,139,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3128,3299,4101,4212,4364,4615,4765,4888,5001,5122,5256,5387,5514,5645,5779,5879,6044,6159,6298,6431,11119,12472,12571,12708,12796,12935,13038,13179", "endColumns": "170,132,110,151,92,149,122,112,120,133,130,126,130,133,99,164,114,138,132,125,133,98,136,87,138,102,140,106", "endOffsets": "3294,3427,4207,4359,4452,4760,4883,4996,5117,5251,5382,5509,5640,5774,5874,6039,6154,6293,6426,6552,11248,12566,12703,12791,12930,13033,13174,13281"}}]}]}