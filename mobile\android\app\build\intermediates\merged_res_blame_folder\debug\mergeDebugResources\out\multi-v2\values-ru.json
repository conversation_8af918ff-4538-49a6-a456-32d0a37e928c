{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "134", "startColumns": "4", "startOffsets": "12351", "endColumns": "100", "endOffsets": "12447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,237,330,419,493,568,657,726,793,870,949,1038,1135,1207,1291,1384,1459,1541,1624,1701,1773,1848,1933,2005,2085,2155", "endColumns": "73,107,92,88,73,74,88,68,66,76,78,88,96,71,83,92,74,81,82,76,71,74,84,71,79,69,84", "endOffsets": "124,232,325,414,488,563,652,721,788,865,944,1033,1130,1202,1286,1379,1454,1536,1619,1696,1768,1843,1928,2000,2080,2150,2235"}, "to": {"startLines": "35,39,40,44,65,67,68,70,83,84,85,120,121,122,124,126,127,128,129,130,131,132,133,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3103,3586,3694,4113,6774,6925,7000,7152,8137,8204,8281,11141,11230,11327,11543,11709,11802,11877,11959,12042,12119,12191,12266,12452,12524,12604,12674", "endColumns": "73,107,92,88,73,74,88,68,66,76,78,88,96,71,83,92,74,81,82,76,71,74,84,71,79,69,84", "endOffsets": "3172,3689,3782,4197,6843,6995,7084,7216,8199,8276,8355,11225,11322,11394,11622,11797,11872,11954,12037,12114,12186,12261,12346,12519,12599,12669,12754"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,244,380,487,625,720,896,1018,1136,1256,1387,1529,1668,1800,1943,2042,2229,2364,2505,2662,2787,2931,3032,3184,3283,3429,3539,3696", "endColumns": "188,135,106,137,94,175,121,117,119,130,141,138,131,142,98,186,134,140,156,124,143,100,151,98,145,109,156,129", "endOffsets": "239,375,482,620,715,891,1013,1131,1251,1382,1524,1663,1795,1938,2037,2224,2359,2500,2657,2782,2926,3027,3179,3278,3424,3534,3691,3821"}, "to": {"startLines": "36,37,45,46,47,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,123,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3177,3366,4202,4309,4447,4707,4883,5005,5123,5243,5374,5516,5655,5787,5930,6029,6216,6351,6492,6649,11399,12759,12860,13012,13111,13257,13367,13524", "endColumns": "188,135,106,137,94,175,121,117,119,130,141,138,131,142,98,186,134,140,156,124,143,100,151,98,145,109,156,129", "endOffsets": "3361,3497,4304,4442,4537,4878,5000,5118,5238,5369,5511,5650,5782,5925,6024,6211,6346,6487,6644,6769,11538,12855,13007,13106,13252,13362,13519,13649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "386,501,603,702,788,893,1014,1093,1169,1261,1355,1450,1543,1638,1732,1828,1923,2015,2107,2196,2302,2409,2507,2616,2723,2837,3003,11627", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "496,598,697,783,888,1009,1088,1164,1256,1350,1445,1538,1633,1727,1823,1918,2010,2102,2191,2297,2404,2502,2611,2718,2832,2998,3098,11704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,336,420,517,654,746,812,911,988,1051,1169,1234,1291,1361,1422,1476,1592,1649,1711,1765,1839,1967,2055,2141,2248,2332,2417,2508,2575,2641,2713,2791,2887,2967,3043,3120,3197,3286,3359,3449,3544,3618,3699,3792,3847,3913,3999,4084,4146,4210,4273,4371,4471,4566,4668", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "331,415,512,649,741,807,906,983,1046,1164,1229,1286,1356,1417,1471,1587,1644,1706,1760,1834,1962,2050,2136,2243,2327,2412,2503,2570,2636,2708,2786,2882,2962,3038,3115,3192,3281,3354,3444,3539,3613,3694,3787,3842,3908,3994,4079,4141,4205,4268,4366,4466,4561,4663,4743"}, "to": {"startLines": "2,38,41,42,43,48,49,66,69,71,72,73,74,75,76,77,78,79,80,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3502,3787,3884,4021,4542,4608,6848,7089,7221,7339,7404,7461,7531,7592,7646,7762,7819,7881,7935,8009,8360,8448,8534,8641,8725,8810,8901,8968,9034,9106,9184,9280,9360,9436,9513,9590,9679,9752,9842,9937,10011,10092,10185,10240,10306,10392,10477,10539,10603,10666,10764,10864,10959,11061", "endLines": "7,38,41,42,43,48,49,66,69,71,72,73,74,75,76,77,78,79,80,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "381,3581,3879,4016,4108,4603,4702,6920,7147,7334,7399,7456,7526,7587,7641,7757,7814,7876,7930,8004,8132,8443,8529,8636,8720,8805,8896,8963,9029,9101,9179,9275,9355,9431,9508,9585,9674,9747,9837,9932,10006,10087,10180,10235,10301,10387,10472,10534,10598,10661,10759,10859,10954,11056,11136"}}]}]}