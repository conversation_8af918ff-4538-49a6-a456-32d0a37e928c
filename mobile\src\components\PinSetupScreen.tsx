import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, TextInput } from 'react-native';
import { pinService, UserData } from '../services/pinService';

interface PinSetupScreenProps {
  userData: UserData;
  onPinSetupComplete: () => void;
  onSkip: () => void;
}

export const PinSetupScreen: React.FC<PinSetupScreenProps> = ({
  userData,
  onPinSetupComplete,
  onSkip,
}) => {
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSetupPin = async () => {
    if (!pin || pin.length < 4) {
      Alert.alert('Error', 'PIN must be at least 4 digits');
      return;
    }

    if (pin !== confirmPin) {
      Alert.alert('Error', 'PINs do not match. Please try again.');
      return;
    }

    setIsLoading(true);
    
    try {
      const success = await pinService.savePin(pin, userData);
      
      if (success) {
        Alert.alert(
          'PIN Setup Complete!',
          'Your PIN has been saved securely. You can now use it to quickly access the app.',
          [{ text: 'OK', onPress: onPinSetupComplete }]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to setup PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔐 Setup PIN</Text>
      <Text style={styles.subtitle}>
        Create a PIN for quick access to your account
      </Text>
      
      <View style={styles.content}>
        <Text style={styles.infoText}>
          Set up a 4-digit PIN to quickly access AutoFlow without entering your email and password every time.
        </Text>
        
        <TextInput
          style={styles.input}
          placeholder="Enter 4-digit PIN"
          value={pin}
          onChangeText={setPin}
          keyboardType="numeric"
          secureTextEntry
          maxLength={6}
        />
        
        <TextInput
          style={styles.input}
          placeholder="Confirm PIN"
          value={confirmPin}
          onChangeText={setConfirmPin}
          keyboardType="numeric"
          secureTextEntry
          maxLength={6}
        />
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={handleSetupPin}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Setting up...' : 'Setup PIN'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.skipButton} 
          onPress={onSkip}
          disabled={isLoading}
        >
          <Text style={styles.skipButtonText}>Skip for now</Text>
        </TouchableOpacity>
      </View>
      
      <Text style={styles.footer}>
        🔒 Your PIN is stored securely on this device only{'\n'}
        📱 Use it for quick access to AutoFlow
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#14b8a6',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  content: {
    alignItems: 'center',
    marginBottom: 40,
  },
  infoText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 25,
    color: '#333',
    lineHeight: 22,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 15,
    fontSize: 18,
    backgroundColor: 'white',
    width: '100%',
    maxWidth: 200,
    textAlign: 'center',
    color: '#111827', // Dark gray text
    letterSpacing: 8,
  },
  button: {
    backgroundColor: '#14b8a6',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginTop: 20,
    minWidth: 200,
  },
  buttonDisabled: {
    backgroundColor: '#94a3b8',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  skipButton: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    marginTop: 10,
  },
  skipButtonText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
  footer: {
    fontSize: 14,
    textAlign: 'center',
    color: '#10b981',
    lineHeight: 20,
  },
});
