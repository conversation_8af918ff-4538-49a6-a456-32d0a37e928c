{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "275,393,504,621,706,812,935,1024,1109,1200,1293,1388,1482,1582,1675,1770,1867,1958,2049,2134,2245,2354,2456,2567,2677,2785,2956,11560", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "388,499,616,701,807,930,1019,1104,1195,1288,1383,1477,1577,1670,1765,1862,1953,2044,2129,2240,2349,2451,2562,2672,2780,2951,3051,11641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,310,412,529,615,681,781,863,926,1017,1082,1144,1213,1275,1329,1467,1524,1585,1639,1712,1865,1950,2034,2143,2224,2309,2399,2466,2532,2610,2695,2780,2852,2932,3012,3083,3175,3247,3344,3441,3515,3589,3691,3747,3819,3907,3999,4061,4125,4188,4304,4412,4521,4629", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,101,116,85,65,99,81,62,90,64,61,68,61,53,137,56,60,53,72,152,84,83,108,80,84,89,66,65,77,84,84,71,79,79,70,91,71,96,96,73,73,101,55,71,87,91,61,63,62,115,107,108,107,90", "endOffsets": "220,305,407,524,610,676,776,858,921,1012,1077,1139,1208,1270,1324,1462,1519,1580,1634,1707,1860,1945,2029,2138,2219,2304,2394,2461,2527,2605,2690,2775,2847,2927,3007,3078,3170,3242,3339,3436,3510,3584,3686,3742,3814,3902,3994,4056,4120,4183,4299,4407,4516,4624,4715"}, "to": {"startLines": "2,36,38,39,40,45,46,63,66,68,69,70,71,72,73,74,75,76,77,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3409,3586,3688,3805,4355,4421,6705,6941,7076,7167,7232,7294,7363,7425,7479,7617,7674,7735,7789,7862,8242,8327,8411,8520,8601,8686,8776,8843,8909,8987,9072,9157,9229,9309,9389,9460,9552,9624,9721,9818,9892,9966,10068,10124,10196,10284,10376,10438,10502,10565,10681,10789,10898,11006", "endLines": "5,36,38,39,40,45,46,63,66,68,69,70,71,72,73,74,75,76,77,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,84,101,116,85,65,99,81,62,90,64,61,68,61,53,137,56,60,53,72,152,84,83,108,80,84,89,66,65,77,84,84,71,79,79,70,91,71,96,96,73,73,101,55,71,87,91,61,63,62,115,107,108,107,90", "endOffsets": "270,3489,3683,3800,3886,4416,4516,6782,6999,7162,7227,7289,7358,7420,7474,7612,7669,7730,7784,7857,8010,8322,8406,8515,8596,8681,8771,8838,8904,8982,9067,9152,9224,9304,9384,9455,9547,9619,9716,9813,9887,9961,10063,10119,10191,10279,10371,10433,10497,10560,10676,10784,10893,11001,11092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,211,334,451,621,712,870,992,1120,1240,1392,1545,1685,1823,1976,2079,2256,2397,2540,2687,2820,2953,3049,3181,3269,3393,3503,3649", "endColumns": "155,122,116,169,90,157,121,127,119,151,152,139,137,152,102,176,140,142,146,132,132,95,131,87,123,109,145,104", "endOffsets": "206,329,446,616,707,865,987,1115,1235,1387,1540,1680,1818,1971,2074,2251,2392,2535,2682,2815,2948,3044,3176,3264,3388,3498,3644,3749"}, "to": {"startLines": "34,35,42,43,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,120,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3130,3286,3977,4094,4264,4521,4679,4801,4929,5049,5201,5354,5494,5632,5785,5888,6065,6206,6349,6496,11345,12703,12799,12931,13019,13143,13253,13399", "endColumns": "155,122,116,169,90,157,121,127,119,151,152,139,137,152,102,176,140,142,146,132,132,95,131,87,123,109,145,104", "endOffsets": "3281,3404,4089,4259,4350,4674,4796,4924,5044,5196,5349,5489,5627,5780,5883,6060,6201,6344,6491,6624,11473,12794,12926,13014,13138,13248,13394,13499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12294", "endColumns": "100", "endOffsets": "12390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,221,307,383,453,537,609,677,755,836,920,1012,1084,1166,1253,1337,1422,1505,1585,1656,1726,1814,1886,1966,2040", "endColumns": "73,91,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "124,216,302,378,448,532,604,672,750,831,915,1007,1079,1161,1248,1332,1417,1500,1580,1651,1721,1809,1881,1961,2035,2117"}, "to": {"startLines": "33,37,41,62,64,65,67,80,81,82,117,118,119,121,123,124,125,126,127,128,129,130,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3056,3494,3891,6629,6787,6857,7004,8015,8083,8161,11097,11181,11273,11478,11646,11733,11817,11902,11985,12065,12136,12206,12395,12467,12547,12621", "endColumns": "73,91,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "3125,3581,3972,6700,6852,6936,7071,8078,8156,8237,11176,11268,11340,11555,11728,11812,11897,11980,12060,12131,12201,12289,12462,12542,12616,12698"}}]}]}