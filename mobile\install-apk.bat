@echo off
echo ========================================
echo AutoFlow Mobile - APK Installation Script
echo ========================================
echo.

set APK_PATH=android\app\build\outputs\apk\debug\app-debug.apk

echo Checking if APK exists...
if not exist "%APK_PATH%" (
    echo ❌ APK not found at: %APK_PATH%
    echo.
    echo Please build the APK first using:
    echo   build-apk.bat
    echo.
    pause
    exit /b 1
)

echo ✅ APK found: %APK_PATH%
echo.

echo Checking for connected devices...
adb devices
echo.

echo Installing APK on connected device...
adb install "%APK_PATH%"

if %errorlevel% equ 0 (
    echo.
    echo ✅ Installation successful!
    echo.
    echo The AutoFlow app should now be available on your device.
    echo Look for "AutoFlow" in your app drawer.
) else (
    echo.
    echo ❌ Installation failed!
    echo.
    echo Make sure:
    echo - Your device is connected via USB
    echo - USB debugging is enabled
    echo - Device is authorized for debugging
    echo.
    echo You can also install manually by:
    echo 1. Copy %APK_PATH% to your device
    echo 2. Enable "Unknown Sources" in device settings
    echo 3. Open the APK file to install
)

echo.
pause
