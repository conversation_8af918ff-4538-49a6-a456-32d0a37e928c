@echo off
echo ========================================
echo AutoFlow Mobile - Force Install New Version
echo ========================================
echo.

echo [1/4] Uninstalling old version...
adb uninstall com.autoflowmobile
echo.

echo [2/4] Creating fresh bundle...
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res
if %errorlevel% neq 0 (
    echo ❌ Bundle creation failed!
    pause
    exit /b 1
)

echo.
echo [3/4] Building fresh APK...
cd android
gradlew.bat assembleDebug
cd ..
if %errorlevel% neq 0 (
    echo ❌ APK build failed!
    pause
    exit /b 1
)

echo.
echo [4/4] Installing fresh APK...
adb install android\app\build\outputs\apk\debug\app-debug.apk
if %errorlevel% neq 0 (
    echo ❌ Installation failed!
    pause
    exit /b 1
)

echo.
echo ✅ Fresh APK installed successfully!
echo.
echo The new version with REAL authentication should now be on your device.
echo Look for "AutoFlow" in your app drawer.
echo.
pause
