{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,357,471,630,721,872,985,1097,1218,1356,1490,1619,1747,1889,1987,2150,2275,2415,2561,2687,2819,2929,3077,3168,3294,3397,3538", "endColumns": "169,131,113,158,90,150,112,111,120,137,133,128,127,141,97,162,124,139,145,125,131,109,147,90,125,102,140,109", "endOffsets": "220,352,466,625,716,867,980,1092,1213,1351,1485,1614,1742,1884,1982,2145,2270,2410,2556,2682,2814,2924,3072,3163,3289,3392,3533,3643"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,119,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3072,3242,4032,4146,4305,4553,4704,4817,4929,5050,5188,5322,5451,5579,5721,5819,5982,6107,6247,6393,10962,12223,12333,12481,12572,12698,12801,12942", "endColumns": "169,131,113,158,90,150,112,111,120,137,133,128,127,141,97,162,124,139,145,125,131,109,147,90,125,102,140,109", "endOffsets": "3237,3369,4141,4300,4391,4699,4812,4924,5045,5183,5317,5446,5574,5716,5814,5977,6102,6242,6388,6514,11089,12328,12476,12567,12693,12796,12937,13047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,301,398,520,601,665,758,837,900,993,1059,1117,1190,1254,1310,1432,1489,1551,1607,1683,1817,1902,1988,2096,2177,2256,2346,2413,2479,2557,2640,2728,2803,2882,2955,3026,3120,3198,3287,3377,3451,3532,3619,3672,3739,3820,3904,3966,4030,4093,4201,4302,4404,4507", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,96,121,80,63,92,78,62,92,65,57,72,63,55,121,56,61,55,75,133,84,85,107,80,78,89,66,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,80", "endOffsets": "219,296,393,515,596,660,753,832,895,988,1054,1112,1185,1249,1305,1427,1484,1546,1602,1678,1812,1897,1983,2091,2172,2251,2341,2408,2474,2552,2635,2723,2798,2877,2950,3021,3115,3193,3282,3372,3446,3527,3614,3667,3734,3815,3899,3961,4025,4088,4196,4297,4399,4502,4583"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,68,69,70,71,72,73,74,75,76,77,78,79,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3374,3657,3754,3876,4396,4460,6587,6826,6889,6982,7048,7106,7179,7243,7299,7421,7478,7540,7596,7672,7956,8041,8127,8235,8316,8395,8485,8552,8618,8696,8779,8867,8942,9021,9094,9165,9259,9337,9426,9516,9590,9671,9758,9811,9878,9959,10043,10105,10169,10232,10340,10441,10543,10646", "endLines": "5,36,39,40,41,46,47,64,67,68,69,70,71,72,73,74,75,76,77,78,79,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "endColumns": "12,76,96,121,80,63,92,78,62,92,65,57,72,63,55,121,56,61,55,75,133,84,85,107,80,78,89,66,65,77,82,87,74,78,72,70,93,77,88,89,73,80,86,52,66,80,83,61,63,62,107,100,101,102,80", "endOffsets": "269,3446,3749,3871,3952,4455,4548,6661,6884,6977,7043,7101,7174,7238,7294,7416,7473,7535,7591,7667,7801,8036,8122,8230,8311,8390,8480,8547,8613,8691,8774,8862,8937,9016,9089,9160,9254,9332,9421,9511,9585,9666,9753,9806,9873,9954,10038,10100,10164,10227,10335,10436,10538,10641,10722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,251,337,412,480,554,640,714,790,874,953,1025,1103,1181,1255,1342,1426,1503,1574,1644,1733,1811,1896", "endColumns": "75,119,85,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "126,246,332,407,475,549,635,709,785,869,948,1020,1098,1176,1250,1337,1421,1498,1569,1639,1728,1806,1891,1965"}, "to": {"startLines": "33,37,38,42,63,65,66,80,81,116,117,118,120,122,123,124,125,126,127,128,129,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2996,3451,3571,3957,6519,6666,6740,7806,7880,10727,10811,10890,11094,11255,11333,11407,11494,11578,11655,11726,11796,11986,12064,12149", "endColumns": "75,119,85,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "3067,3566,3652,4027,6582,6735,6821,7875,7951,10806,10885,10957,11167,11328,11402,11489,11573,11650,11721,11791,11880,12059,12144,12218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,392,497,604,689,793,913,991,1067,1159,1253,1348,1442,1542,1636,1732,1827,1919,2011,2093,2204,2307,2406,2521,2635,2738,2893,11172", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "387,492,599,684,788,908,986,1062,1154,1248,1343,1437,1537,1631,1727,1822,1914,2006,2088,2199,2302,2401,2516,2630,2733,2888,2991,11250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "130", "startColumns": "4", "startOffsets": "11885", "endColumns": "100", "endOffsets": "11981"}}]}]}