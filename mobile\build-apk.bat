@echo off
echo ========================================
echo AutoFlow Mobile - APK Build Script
echo ========================================
echo.

echo [1/5] Checking environment...
call npx react-native doctor
if %errorlevel% neq 0 (
    echo.
    echo ❌ Environment check failed!
    echo Please fix the issues shown above before building.
    echo See BUILD_INSTALLATION_GUIDE.md for help.
    pause
    exit /b 1
)

echo.
echo [2/5] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Dependency installation failed!
    pause
    exit /b 1
)

echo.
echo [3/5] Cleaning previous builds...
cd android
call gradlew.bat clean
cd ..

echo.
echo [4/5] Building APK...
call npx react-native build-android --mode=debug
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    echo Check the error messages above.
    pause
    exit /b 1
)

echo.
echo [5/5] Build completed successfully! ✅
echo.
echo APK Location: android\app\build\outputs\apk\debug\app-debug.apk
echo.
echo To install on device:
echo   adb install android\app\build\outputs\apk\debug\app-debug.apk
echo.
echo Or copy the APK file to your device and install manually.
echo.
pause
