{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,216,299,374,445,528,603,679,760,840,909,987,1066,1142,1222,1302,1379,1450,1520,1603,1677,1759", "endColumns": "75,84,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "126,211,294,369,440,523,598,674,755,835,904,982,1061,1137,1217,1297,1374,1445,1515,1598,1672,1754,1833"}, "to": {"startLines": "33,37,41,62,64,65,79,80,115,116,117,119,121,122,123,124,125,126,127,128,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2929,3345,3757,6336,6484,6555,7592,7667,10437,10518,10598,10788,10946,11025,11101,11181,11261,11338,11409,11479,11663,11737,11819", "endColumns": "75,84,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "3000,3425,3835,6406,6550,6633,7662,7738,10513,10593,10662,10861,11020,11096,11176,11256,11333,11404,11474,11557,11732,11814,11893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,203,317,427,595,683,826,937,1053,1177,1318,1451,1578,1714,1857,1956,2114,2238,2387,2537,2663,2784,2876,2998,3083,3200,3298,3426", "endColumns": "147,113,109,167,87,142,110,115,123,140,132,126,135,142,98,157,123,148,149,125,120,91,121,84,116,97,127,93", "endOffsets": "198,312,422,590,678,821,932,1048,1172,1313,1446,1573,1709,1852,1951,2109,2233,2382,2532,2658,2779,2871,2993,3078,3195,3293,3421,3515"}, "to": {"startLines": "34,35,42,43,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,118,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3005,3153,3840,3950,4118,4356,4499,4610,4726,4850,4991,5124,5251,5387,5530,5629,5787,5911,6060,6210,10667,11898,11990,12112,12197,12314,12412,12540", "endColumns": "147,113,109,167,87,142,110,115,123,140,132,126,135,142,98,157,123,148,149,125,120,91,121,84,116,97,127,93", "endOffsets": "3148,3262,3945,4113,4201,4494,4605,4721,4845,4986,5119,5246,5382,5525,5624,5782,5906,6055,6205,6331,10783,11985,12107,12192,12309,12407,12535,12629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,377,471,587,672,772,885,963,1039,1130,1223,1316,1410,1504,1597,1692,1790,1881,1972,2051,2159,2266,2362,2475,2578,2679,2832,10866", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "372,466,582,667,767,880,958,1034,1125,1218,1311,1405,1499,1592,1687,1785,1876,1967,2046,2154,2261,2357,2470,2573,2674,2827,2924,10941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "129", "startColumns": "4", "startOffsets": "11562", "endColumns": "100", "endOffsets": "11658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,305,405,554,632,696,782,855,915,1002,1064,1126,1194,1259,1315,1433,1491,1552,1608,1683,1809,1895,1975,2086,2164,2244,2330,2397,2463,2531,2605,2694,2766,2844,2914,2987,3071,3148,3236,3325,3399,3472,3557,3606,3672,3752,3835,3897,3961,4024,4132,4227,4328,4423", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,99,148,77,63,85,72,59,86,61,61,67,64,55,117,57,60,55,74,125,85,79,110,77,79,85,66,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,79", "endOffsets": "222,300,400,549,627,691,777,850,910,997,1059,1121,1189,1254,1310,1428,1486,1547,1603,1678,1804,1890,1970,2081,2159,2239,2325,2392,2458,2526,2600,2689,2761,2839,2909,2982,3066,3143,3231,3320,3394,3467,3552,3601,3667,3747,3830,3892,3956,4019,4127,4222,4323,4418,4498"}, "to": {"startLines": "2,36,38,39,40,45,46,63,66,67,68,69,70,71,72,73,74,75,76,77,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3267,3430,3530,3679,4206,4270,6411,6638,6698,6785,6847,6909,6977,7042,7098,7216,7274,7335,7391,7466,7743,7829,7909,8020,8098,8178,8264,8331,8397,8465,8539,8628,8700,8778,8848,8921,9005,9082,9170,9259,9333,9406,9491,9540,9606,9686,9769,9831,9895,9958,10066,10161,10262,10357", "endLines": "5,36,38,39,40,45,46,63,66,67,68,69,70,71,72,73,74,75,76,77,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "endColumns": "12,77,99,148,77,63,85,72,59,86,61,61,67,64,55,117,57,60,55,74,125,85,79,110,77,79,85,66,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,79", "endOffsets": "272,3340,3525,3674,3752,4265,4351,6479,6693,6780,6842,6904,6972,7037,7093,7211,7269,7330,7386,7461,7587,7824,7904,8015,8093,8173,8259,8326,8392,8460,8534,8623,8695,8773,8843,8916,9000,9077,9165,9254,9328,9401,9486,9535,9601,9681,9764,9826,9890,9953,10061,10156,10257,10352,10432"}}]}]}