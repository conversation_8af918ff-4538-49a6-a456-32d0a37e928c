{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,310,413,533,614,678,770,849,914,1004,1072,1134,1207,1271,1325,1451,1509,1571,1625,1701,1844,1931,2013,2122,2204,2286,2373,2440,2506,2581,2661,2748,2821,2898,2971,3045,3138,3215,3308,3406,3480,3561,3660,3713,3779,3868,3956,4018,4082,4145,4261,4364,4471,4575", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "223,305,408,528,609,673,765,844,909,999,1067,1129,1202,1266,1320,1446,1504,1566,1620,1696,1839,1926,2008,2117,2199,2281,2368,2435,2501,2576,2656,2743,2816,2893,2966,3040,3133,3210,3303,3401,3475,3556,3655,3708,3774,3863,3951,4013,4077,4140,4256,4359,4466,4570,4656"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3351,3642,3745,3865,4390,4454,6619,6851,6986,7076,7144,7206,7279,7343,7397,7523,7581,7643,7697,7773,8074,8161,8243,8352,8434,8516,8603,8670,8736,8811,8891,8978,9051,9128,9201,9275,9368,9445,9538,9636,9710,9791,9890,9943,10009,10098,10186,10248,10312,10375,10491,10594,10701,10805", "endLines": "5,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,81,102,119,80,63,91,78,64,89,67,61,72,63,53,125,57,61,53,75,142,86,81,108,81,81,86,66,65,74,79,86,72,76,72,73,92,76,92,97,73,80,98,52,65,88,87,61,63,62,115,102,106,103,85", "endOffsets": "273,3428,3740,3860,3941,4449,4541,6693,6911,7071,7139,7201,7274,7338,7392,7518,7576,7638,7692,7768,7911,8156,8238,8347,8429,8511,8598,8665,8731,8806,8886,8973,9046,9123,9196,9270,9363,9440,9533,9631,9705,9786,9885,9938,10004,10093,10181,10243,10307,10370,10486,10589,10696,10800,10886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12081", "endColumns": "100", "endOffsets": "12177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,328,440,591,686,832,941,1062,1191,1330,1477,1602,1731,1878,1980,2145,2273,2413,2562,2688,2817,2912,3044,3130,3253,3358,3500", "endColumns": "154,117,111,150,94,145,108,120,128,138,146,124,128,146,101,164,127,139,148,125,128,94,131,85,122,104,141,103", "endOffsets": "205,323,435,586,681,827,936,1057,1186,1325,1472,1597,1726,1873,1975,2140,2268,2408,2557,2683,2812,2907,3039,3125,3248,3353,3495,3599"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,120,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3078,3233,4032,4144,4295,4546,4692,4801,4922,5051,5190,5337,5462,5591,5738,5840,6005,6133,6273,6422,11137,12502,12597,12729,12815,12938,13043,13185", "endColumns": "154,117,111,150,94,145,108,120,128,138,146,124,128,146,101,164,127,139,148,125,128,94,131,85,122,104,141,103", "endOffsets": "3228,3346,4139,4290,4385,4687,4796,4917,5046,5185,5332,5457,5586,5733,5835,6000,6128,6268,6417,6543,11261,12592,12724,12810,12933,13038,13180,13284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,241,333,419,490,560,643,713,792,871,959,1043,1117,1206,1290,1366,1447,1529,1604,1682,1756,1846,1918,2004,2080", "endColumns": "68,116,91,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "119,236,328,414,485,555,638,708,787,866,954,1038,1112,1201,1285,1361,1442,1524,1599,1677,1751,1841,1913,1999,2075,2161"}, "to": {"startLines": "33,37,38,42,63,65,66,68,81,82,117,118,119,121,123,124,125,126,127,128,129,130,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3009,3433,3550,3946,6548,6698,6768,6916,7916,7995,10891,10979,11063,11266,11441,11525,11601,11682,11764,11839,11917,11991,12182,12254,12340,12416", "endColumns": "68,116,91,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "3073,3545,3637,4027,6614,6763,6846,6981,7990,8069,10974,11058,11132,11350,11520,11596,11677,11759,11834,11912,11986,12076,12249,12335,12411,12497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "278,386,492,599,688,789,907,992,1072,1164,1258,1355,1449,1548,1642,1738,1833,1925,2017,2102,2209,2320,2422,2530,2638,2745,2910,11355", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "381,487,594,683,784,902,987,1067,1159,1253,1350,1444,1543,1637,1733,1828,1920,2012,2097,2204,2315,2417,2525,2633,2740,2905,3004,11436"}}]}]}