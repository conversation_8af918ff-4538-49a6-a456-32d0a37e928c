{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "11776", "endColumns": "100", "endOffsets": "11872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,233,319,394,467,535,615,683,758,836,918,1006,1080,1159,1240,1317,1400,1483,1561,1635,1706,1789,1864,1948,2018", "endColumns": "70,106,85,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "121,228,314,389,462,530,610,678,753,831,913,1001,1075,1154,1235,1312,1395,1478,1556,1630,1701,1784,1859,1943,2013,2092"}, "to": {"startLines": "33,37,38,42,63,65,66,80,81,82,117,118,119,121,123,124,125,126,127,128,129,130,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2993,3441,3548,3932,6390,6531,6599,7643,7711,7786,10617,10699,10787,10984,11146,11227,11304,11387,11470,11548,11622,11693,11877,11952,12036,12106", "endColumns": "70,106,85,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "3059,3543,3629,4002,6458,6594,6674,7706,7781,7859,10694,10782,10856,11058,11222,11299,11382,11465,11543,11617,11688,11771,11947,12031,12101,12180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,347,455,600,690,830,938,1049,1168,1297,1426,1555,1686,1817,1916,2069,2190,2329,2464,2581,2704,2811,2950,3035,3152,3250,3380", "endColumns": "161,129,107,144,89,139,107,110,118,128,128,128,130,130,98,152,120,138,134,116,122,106,138,84,116,97,129,99", "endOffsets": "212,342,450,595,685,825,933,1044,1163,1292,1421,1550,1681,1812,1911,2064,2185,2324,2459,2576,2699,2806,2945,3030,3147,3245,3375,3475"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,120,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3064,3226,4007,4115,4260,4499,4639,4747,4858,4977,5106,5235,5364,5495,5626,5725,5878,5999,6138,6273,10861,12185,12292,12431,12516,12633,12731,12861", "endColumns": "161,129,107,144,89,139,107,110,118,128,128,128,130,130,98,152,120,138,134,116,122,106,138,84,116,97,129,99", "endOffsets": "3221,3351,4110,4255,4345,4634,4742,4853,4972,5101,5230,5359,5490,5621,5720,5873,5994,6133,6268,6385,10979,12287,12426,12511,12628,12726,12856,12956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "271,377,476,587,673,775,892,973,1050,1142,1236,1332,1434,1543,1637,1738,1832,1924,2017,2100,2211,2315,2414,2524,2626,2725,2891,11063", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "372,471,582,668,770,887,968,1045,1137,1231,1327,1429,1538,1632,1733,1827,1919,2012,2095,2206,2310,2409,2519,2621,2720,2886,2988,11141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,306,405,522,604,668,753,821,885,972,1036,1095,1167,1231,1285,1404,1464,1525,1579,1652,1785,1869,1962,2070,2150,2229,2317,2384,2450,2523,2602,2688,2761,2836,2910,2982,3070,3147,3238,3330,3402,3476,3567,3621,3690,3773,3859,3921,3985,4048,4151,4255,4352,4457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,98,116,81,63,84,67,63,86,63,58,71,63,53,118,59,60,53,72,132,83,92,107,79,78,87,66,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,80", "endOffsets": "216,301,400,517,599,663,748,816,880,967,1031,1090,1162,1226,1280,1399,1459,1520,1574,1647,1780,1864,1957,2065,2145,2224,2312,2379,2445,2518,2597,2683,2756,2831,2905,2977,3065,3142,3233,3325,3397,3471,3562,3616,3685,3768,3854,3916,3980,4043,4146,4250,4347,4452,4533"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,68,69,70,71,72,73,74,75,76,77,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3356,3634,3733,3850,4350,4414,6463,6679,6743,6830,6894,6953,7025,7089,7143,7262,7322,7383,7437,7510,7864,7948,8041,8149,8229,8308,8396,8463,8529,8602,8681,8767,8840,8915,8989,9061,9149,9226,9317,9409,9481,9555,9646,9700,9769,9852,9938,10000,10064,10127,10230,10334,10431,10536", "endLines": "5,36,39,40,41,46,47,64,67,68,69,70,71,72,73,74,75,76,77,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,84,98,116,81,63,84,67,63,86,63,58,71,63,53,118,59,60,53,72,132,83,92,107,79,78,87,66,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,80", "endOffsets": "266,3436,3728,3845,3927,4409,4494,6526,6738,6825,6889,6948,7020,7084,7138,7257,7317,7378,7432,7505,7638,7943,8036,8144,8224,8303,8391,8458,8524,8597,8676,8762,8835,8910,8984,9056,9144,9221,9312,9404,9476,9550,9641,9695,9764,9847,9933,9995,10059,10122,10225,10329,10426,10531,10612"}}]}]}