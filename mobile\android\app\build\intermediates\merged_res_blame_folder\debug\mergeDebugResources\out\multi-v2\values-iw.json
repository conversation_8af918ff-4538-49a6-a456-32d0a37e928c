{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "134", "startColumns": "4", "startOffsets": "11647", "endColumns": "100", "endOffsets": "11743"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "369,474,574,682,766,868,984,1063,1141,1232,1326,1420,1514,1614,1707,1802,1895,1986,2078,2159,2264,2367,2465,2570,2672,2774,2928,10948", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "469,569,677,761,863,979,1058,1136,1227,1321,1415,1509,1609,1702,1797,1890,1981,2073,2154,2259,2362,2460,2565,2667,2769,2923,3020,11025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,319,396,489,602,682,747,835,905,968,1060,1120,1179,1242,1303,1362,1464,1521,1580,1638,1706,1817,1898,1980,2082,2153,2226,2314,2381,2447,2520,2596,2682,2752,2827,2909,2977,3062,3132,3222,3313,3387,3460,3549,3600,3667,3749,3834,3896,3960,4023,4117,4212,4302,4398", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,58,101,56,58,57,67,110,80,81,101,70,72,87,66,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,74", "endOffsets": "314,391,484,597,677,742,830,900,963,1055,1115,1174,1237,1298,1357,1459,1516,1575,1633,1701,1812,1893,1975,2077,2148,2221,2309,2376,2442,2515,2591,2677,2747,2822,2904,2972,3057,3127,3217,3308,3382,3455,3544,3595,3662,3744,3829,3891,3955,4018,4112,4207,4297,4393,4468"}, "to": {"startLines": "2,38,41,42,43,48,49,66,69,71,72,73,74,75,76,77,78,79,80,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3365,3626,3719,3832,4339,4404,6438,6658,6789,6881,6941,7000,7063,7124,7183,7285,7342,7401,7459,7527,7862,7943,8025,8127,8198,8271,8359,8426,8492,8565,8641,8727,8797,8872,8954,9022,9107,9177,9267,9358,9432,9505,9594,9645,9712,9794,9879,9941,10005,10068,10162,10257,10347,10443", "endLines": "7,38,41,42,43,48,49,66,69,71,72,73,74,75,76,77,78,79,80,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,58,101,56,58,57,67,110,80,81,101,70,72,87,66,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,74", "endOffsets": "364,3437,3714,3827,3907,4399,4487,6503,6716,6876,6936,6995,7058,7119,7178,7280,7337,7396,7454,7522,7633,7938,8020,8122,8193,8266,8354,8421,8487,8560,8636,8722,8792,8867,8949,9017,9102,9172,9262,9353,9427,9500,9589,9640,9707,9789,9874,9936,10000,10063,10157,10252,10342,10438,10513"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,221,308,386,456,525,606,674,742,820,898,980,1059,1130,1208,1288,1361,1441,1519,1594,1666,1738,1825,1896,1975,2044", "endColumns": "68,96,86,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,216,303,381,451,520,601,669,737,815,893,975,1054,1125,1203,1283,1356,1436,1514,1589,1661,1733,1820,1891,1970,2039,2114"}, "to": {"startLines": "35,39,40,44,65,67,68,70,83,84,85,120,121,122,124,126,127,128,129,130,131,132,133,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3025,3442,3539,3912,6368,6508,6577,6721,7638,7706,7784,10518,10600,10679,10870,11030,11110,11183,11263,11341,11416,11488,11560,11748,11819,11898,11967", "endColumns": "68,96,86,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3089,3534,3621,3985,6433,6572,6653,6784,7701,7779,7857,10595,10674,10745,10943,11105,11178,11258,11336,11411,11483,11555,11642,11814,11893,11962,12037"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,326,432,584,675,819,933,1042,1162,1291,1415,1546,1660,1787,1881,2032,2153,2296,2438,2551,2671,2769,2896,2987,3107,3205,3332", "endColumns": "150,119,105,151,90,143,113,108,119,128,123,130,113,126,93,150,120,142,141,112,119,97,126,90,119,97,126,96", "endOffsets": "201,321,427,579,670,814,928,1037,1157,1286,1410,1541,1655,1782,1876,2027,2148,2291,2433,2546,2666,2764,2891,2982,3102,3200,3327,3424"}, "to": {"startLines": "36,37,45,46,47,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,123,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3094,3245,3990,4096,4248,4492,4636,4750,4859,4979,5108,5232,5363,5477,5604,5698,5849,5970,6113,6255,10750,12042,12140,12267,12358,12478,12576,12703", "endColumns": "150,119,105,151,90,143,113,108,119,128,123,130,113,126,93,150,120,142,141,112,119,97,126,90,119,97,126,96", "endOffsets": "3240,3360,4091,4243,4334,4631,4745,4854,4974,5103,5227,5358,5472,5599,5693,5844,5965,6108,6250,6363,10865,12135,12262,12353,12473,12571,12698,12795"}}]}]}