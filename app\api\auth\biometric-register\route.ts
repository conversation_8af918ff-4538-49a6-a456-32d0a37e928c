// app/api/auth/biometric-register/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { userId, publicKey } = await request.json();

    if (!userId || !publicKey) {
      return NextResponse.json(
        { error: 'userId and publicKey are required' },
        { status: 400 }
      );
    }

    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response });

    // Verify the user exists
    const { data: user, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError || !user) {
      console.error('Biometric register - user not found:', userError);
      return NextResponse.json(
        { success: false },
        { status: 401 }
      );
    }

    // Store the public key in the database
    // First, check if a biometric key already exists for this user
    const { data: existingKey, error: checkError } = await supabase
      .from('biometric_keys')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingKey) {
      // Update existing key
      const { error: updateError } = await supabase
        .from('biometric_keys')
        .update({
          public_key: publicKey,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Biometric register - update error:', updateError);
        return NextResponse.json(
          { success: false },
          { status: 500 }
        );
      }
    } else {
      // Insert new key
      const { error: insertError } = await supabase
        .from('biometric_keys')
        .insert({
          user_id: userId,
          public_key: publicKey,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (insertError) {
        console.error('Biometric register - insert error:', insertError);
        return NextResponse.json(
          { success: false },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      success: true,
    });

  } catch (error) {
    console.error('Biometric registration error:', error);
    return NextResponse.json(
      { success: false },
      { status: 500 }
    );
  }
}
