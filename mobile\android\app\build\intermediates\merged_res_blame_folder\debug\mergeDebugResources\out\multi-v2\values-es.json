{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,320,424,546,627,692,787,868,931,1020,1089,1152,1226,1290,1346,1464,1522,1584,1640,1720,1859,1948,2030,2141,2222,2302,2392,2459,2525,2604,2686,2774,2848,2925,2995,3074,3158,3242,3334,3434,3508,3589,3691,3744,3811,3904,3993,4055,4119,4182,4295,4388,4492,4586", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "228,315,419,541,622,687,782,863,926,1015,1084,1147,1221,1285,1341,1459,1517,1579,1635,1715,1854,1943,2025,2136,2217,2297,2387,2454,2520,2599,2681,2769,2843,2920,2990,3069,3153,3237,3329,3429,3503,3584,3686,3739,3806,3899,3988,4050,4114,4177,4290,4383,4487,4581,4664"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3354,3657,3761,3883,4414,4479,6658,6892,7024,7113,7182,7245,7319,7383,7439,7557,7615,7677,7733,7813,8181,8270,8352,8463,8544,8624,8714,8781,8847,8926,9008,9096,9170,9247,9317,9396,9480,9564,9656,9756,9830,9911,10013,10066,10133,10226,10315,10377,10441,10504,10617,10710,10814,10908", "endLines": "5,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "12,86,103,121,80,64,94,80,62,88,68,62,73,63,55,117,57,61,55,79,138,88,81,110,80,79,89,66,65,78,81,87,73,76,69,78,83,83,91,99,73,80,101,52,66,92,88,61,63,62,112,92,103,93,82", "endOffsets": "278,3436,3756,3878,3959,4474,4569,6734,6950,7108,7177,7240,7314,7378,7434,7552,7610,7672,7728,7808,7947,8265,8347,8458,8539,8619,8709,8776,8842,8921,9003,9091,9165,9242,9312,9391,9475,9559,9651,9751,9825,9906,10008,10061,10128,10221,10310,10372,10436,10499,10612,10705,10809,10903,10986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,325,434,598,692,837,942,1060,1180,1323,1473,1619,1755,1901,2001,2163,2285,2431,2573,2701,2834,2927,3057,3140,3260,3360,3497", "endColumns": "154,114,108,163,93,144,104,117,119,142,149,145,135,145,99,161,121,145,141,127,132,92,129,82,119,99,136,104", "endOffsets": "205,320,429,593,687,832,937,1055,1175,1318,1468,1614,1750,1896,1996,2158,2280,2426,2568,2696,2829,2922,3052,3135,3255,3355,3492,3597"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,121,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3084,3239,4047,4156,4320,4574,4719,4824,4942,5062,5205,5355,5501,5637,5783,5883,6045,6167,6313,6455,11245,12618,12711,12841,12924,13044,13144,13281", "endColumns": "154,114,108,163,93,144,104,117,119,142,149,145,135,145,99,161,121,145,141,127,132,92,129,82,119,99,136,104", "endOffsets": "3234,3349,4151,4315,4409,4714,4819,4937,5057,5200,5350,5496,5632,5778,5878,6040,6162,6308,6450,6578,11373,12706,12836,12919,13039,13139,13276,13381"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "283,385,498,606,691,792,920,1006,1087,1179,1273,1370,1464,1564,1658,1754,1850,1942,2034,2116,2223,2334,2433,2541,2649,2756,2915,11469", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "380,493,601,686,787,915,1001,1082,1174,1268,1365,1459,1559,1653,1749,1845,1937,2029,2111,2218,2329,2428,2536,2644,2751,2910,3009,11547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "12198", "endColumns": "100", "endOffsets": "12294"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,243,341,424,499,569,652,721,788,868,950,1037,1132,1204,1295,1381,1457,1540,1622,1697,1776,1851,1941,2014,2097,2173", "endColumns": "69,117,97,82,74,69,82,68,66,79,81,86,94,71,90,85,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "120,238,336,419,494,564,647,716,783,863,945,1032,1127,1199,1290,1376,1452,1535,1617,1692,1771,1846,1936,2009,2092,2168,2255"}, "to": {"startLines": "33,37,38,42,63,65,66,68,81,82,83,118,119,120,122,124,125,126,127,128,129,130,131,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3014,3441,3559,3964,6583,6739,6809,6955,7952,8019,8099,10991,11078,11173,11378,11552,11638,11714,11797,11879,11954,12033,12108,12299,12372,12455,12531", "endColumns": "69,117,97,82,74,69,82,68,66,79,81,86,94,71,90,85,75,82,81,74,78,74,89,72,82,75,86", "endOffsets": "3079,3554,3652,4042,6653,6804,6887,7019,8014,8094,8176,11073,11168,11240,11464,11633,11709,11792,11874,11949,12028,12103,12193,12367,12450,12526,12613"}}]}]}