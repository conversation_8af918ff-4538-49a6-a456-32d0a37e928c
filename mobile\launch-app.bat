@echo off
echo ========================================
echo AutoFlow Mobile - App Launcher
echo ========================================
echo.

echo Checking device connection...
adb devices
echo.

echo Checking if AutoFlow app is installed...
adb shell pm list packages | findstr com.autoflowmobile
if %errorlevel% equ 0 (
    echo ✅ AutoFlow app is installed!
) else (
    echo ❌ AutoFlow app is not installed!
    echo Please run build-apk.bat first.
    pause
    exit /b 1
)

echo.
echo Launching AutoFlow app...
adb shell am start -n com.autoflowmobile/.MainActivity

if %errorlevel% equ 0 (
    echo.
    echo ✅ AutoFlow app launched successfully!
    echo.
    echo The app should now be running on your device.
    echo Look for "AutoFlow" in your recent apps or app drawer.
    echo.
    echo If you don't see it in your launcher:
    echo 1. Check your app drawer/all apps
    echo 2. Look in recent apps (square/overview button)
    echo 3. Search for "AutoFlow" in your device's app search
) else (
    echo.
    echo ❌ Failed to launch the app!
    echo Please check the device connection and try again.
)

echo.
echo To view app logs, run:
echo   adb logcat -s ReactNativeJS
echo.
pause
