{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,333,443,594,692,838,947,1055,1170,1299,1426,1561,1681,1819,1919,2075,2199,2338,2479,2602,2725,2821,2949,3036,3155,3252,3381", "endColumns": "154,122,109,150,97,145,108,107,114,128,126,134,119,137,99,155,123,138,140,122,122,95,127,86,118,96,128,99", "endOffsets": "205,328,438,589,687,833,942,1050,1165,1294,1421,1556,1676,1814,1914,2070,2194,2333,2474,2597,2720,2816,2944,3031,3150,3247,3376,3476"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,119,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3050,3205,3996,4106,4257,4513,4659,4768,4876,4991,5120,5247,5382,5502,5640,5740,5896,6020,6159,6300,10789,12112,12208,12336,12423,12542,12639,12768", "endColumns": "154,122,109,150,97,145,108,107,114,128,126,134,119,137,99,155,123,138,140,122,122,95,127,86,118,96,128,99", "endOffsets": "3200,3323,4101,4252,4350,4654,4763,4871,4986,5115,5242,5377,5497,5635,5735,5891,6015,6154,6295,6418,10907,12203,12331,12418,12537,12634,12763,12863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,321,424,540,623,688,781,846,905,992,1054,1114,1180,1242,1296,1404,1461,1522,1577,1648,1768,1859,1945,2063,2149,2235,2323,2390,2456,2527,2605,2688,2761,2837,2910,2981,3073,3146,3236,3329,3403,3474,3565,3617,3685,3769,3854,3916,3980,4043,4147,4253,4349,4457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,102,115,82,64,92,64,58,86,61,59,65,61,53,107,56,60,54,70,119,90,85,117,85,85,87,66,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,85", "endOffsets": "229,316,419,535,618,683,776,841,900,987,1049,1109,1175,1237,1291,1399,1456,1517,1572,1643,1763,1854,1940,2058,2144,2230,2318,2385,2451,2522,2600,2683,2756,2832,2905,2976,3068,3141,3231,3324,3398,3469,3560,3612,3680,3764,3849,3911,3975,4038,4142,4248,4344,4452,4538"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3328,3616,3719,3835,4355,4420,6493,6708,6836,6923,6985,7045,7111,7173,7227,7335,7392,7453,7508,7579,7849,7940,8026,8144,8230,8316,8404,8471,8537,8608,8686,8769,8842,8918,8991,9062,9154,9227,9317,9410,9484,9555,9646,9698,9766,9850,9935,9997,10061,10124,10228,10334,10430,10538", "endLines": "5,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,86,102,115,82,64,92,64,58,86,61,59,65,61,53,107,56,60,54,70,119,90,85,117,85,85,87,66,65,70,77,82,72,75,72,70,91,72,89,92,73,70,90,51,67,83,84,61,63,62,103,105,95,107,85", "endOffsets": "279,3410,3714,3830,3913,4415,4508,6553,6762,6918,6980,7040,7106,7168,7222,7330,7387,7448,7503,7574,7694,7935,8021,8139,8225,8311,8399,8466,8532,8603,8681,8764,8837,8913,8986,9057,9149,9222,9312,9405,9479,9550,9641,9693,9761,9845,9930,9992,10056,10119,10223,10329,10425,10533,10619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,232,327,405,475,543,625,694,768,844,926,1009,1086,1169,1243,1328,1413,1491,1568,1645,1731,1806,1883,1953", "endColumns": "70,105,94,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "121,227,322,400,470,538,620,689,763,839,921,1004,1081,1164,1238,1323,1408,1486,1563,1640,1726,1801,1878,1948,2022"}, "to": {"startLines": "33,37,38,42,63,65,66,68,81,82,117,118,120,122,123,124,125,126,127,128,129,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2979,3415,3521,3918,6423,6558,6626,6767,7699,7773,10624,10706,10912,11070,11153,11227,11312,11397,11475,11552,11629,11816,11891,11968,12038", "endColumns": "70,105,94,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "3045,3516,3611,3991,6488,6621,6703,6831,7768,7844,10701,10784,10984,11148,11222,11307,11392,11470,11547,11624,11710,11886,11963,12033,12107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "284,395,500,608,695,799,910,989,1067,1158,1251,1346,1440,1538,1631,1726,1820,1911,2002,2082,2194,2302,2399,2508,2612,2719,2878,10989", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "390,495,603,690,794,905,984,1062,1153,1246,1341,1435,1533,1626,1721,1815,1906,1997,2077,2189,2297,2394,2503,2607,2714,2873,2974,11065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "130", "startColumns": "4", "startOffsets": "11715", "endColumns": "100", "endOffsets": "11811"}}]}]}