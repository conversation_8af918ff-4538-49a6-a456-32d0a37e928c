// app/api/auth/biometric-verify/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { userId, challenge, signature } = await request.json();

    if (!userId || !challenge || !signature) {
      return NextResponse.json(
        { error: 'userId, challenge, and signature are required' },
        { status: 400 }
      );
    }

    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response });

    // Verify the user exists
    const { data: user, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError || !user) {
      console.error('Biometric verify - user not found:', userError);
      return NextResponse.json(
        { success: false, token: null },
        { status: 401 }
      );
    }

    // In a real implementation, you would:
    // 1. Retrieve the stored public key for this user
    // 2. Verify the signature against the challenge using the public key
    // 3. Generate a JWT token if verification succeeds
    
    // For now, we'll simulate successful verification
    // TODO: Implement actual cryptographic verification
    
    // Generate a session token (simplified)
    const sessionToken = `biometric_session_${userId}_${Date.now()}`;

    return NextResponse.json({
      success: true,
      token: sessionToken,
    });

  } catch (error) {
    console.error('Biometric verification error:', error);
    return NextResponse.json(
      { success: false, token: null },
      { status: 500 }
    );
  }
}
