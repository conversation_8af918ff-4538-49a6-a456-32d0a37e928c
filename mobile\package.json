{"name": "autoflow-mobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace AutoflowMobile.xcworkspace -scheme AutoflowMobile -configuration Release -destination generic/platform=iOS -archivePath AutoflowMobile.xcarchive archive"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/native-stack": "^7.3.23", "@sbaiahmed1/react-native-biometrics": "^0.4.0", "@supabase/supabase-js": "^2.52.0", "react": "18.2.0", "react-native": "0.73.2", "react-native-config": "^1.5.5", "react-native-dotenv": "^3.4.11", "react-native-keychain": "^10.0.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^4.14.1", "react-native-screens": "^3.37.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.3.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/cli-platform-android": "^18.0.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.73.7", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}