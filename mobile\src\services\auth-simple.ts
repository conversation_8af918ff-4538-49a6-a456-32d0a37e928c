// Simple auth service for testing real authentication
import { supabase } from './supabase';

export class SimpleAuthService {
  async login(email: string, password: string): Promise<{ requiresOTP: boolean }> {
    try {
      console.log('🔐 Attempting REAL login with Supabase:', email);
      
      // This is REAL Supabase authentication - no demo data!
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('❌ Login error:', error);
        throw new Error(error.message);
      }

      console.log('✅ Login successful, sending OTP...');
      
      // Send OTP for additional verification
      const { error: otpError } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
        },
      });

      if (otpError) {
        console.error('❌ OTP send error:', otpError);
        throw new Error(otpError.message);
      }

      console.log('📧 OTP sent successfully!');
      return { requiresOTP: true };
    } catch (error) {
      console.error('❌ Auth service error:', error);
      throw error;
    }
  }

  async verifyOTP(email: string, token: string): Promise<any> {
    try {
      console.log('🔍 Verifying OTP with REAL Supabase:', email);
      
      // This is REAL OTP verification with Supabase!
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email',
      });

      if (error) {
        console.error('❌ OTP verification error:', error);
        throw new Error(error.message);
      }

      console.log('✅ OTP verified successfully!');
      return data.user;
    } catch (error) {
      console.error('❌ OTP verification failed:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      console.log('🚪 Logging out from REAL Supabase...');
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('❌ Logout error:', error);
        throw new Error(error.message);
      }

      console.log('✅ Logged out successfully!');
    } catch (error) {
      console.error('❌ Logout failed:', error);
      throw error;
    }
  }

  async getCurrentUser(): Promise<any> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      return user;
    } catch (error) {
      console.error('❌ Get current user error:', error);
      return null;
    }
  }
}

export const authService = new SimpleAuthService();
