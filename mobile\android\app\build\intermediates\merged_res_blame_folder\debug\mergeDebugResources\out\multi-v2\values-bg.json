{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,303,398,531,620,686,783,863,925,1014,1079,1138,1211,1274,1328,1456,1513,1575,1629,1702,1845,1929,2017,2123,2211,2299,2384,2451,2517,2592,2668,2754,2831,2907,2984,3058,3149,3224,3315,3407,3481,3568,3659,3714,3780,3863,3949,4011,4075,4138,4255,4368,4479,4596", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,94,132,88,65,96,79,61,88,64,58,72,62,53,127,56,61,53,72,142,83,87,105,87,87,84,66,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,85", "endOffsets": "215,298,393,526,615,681,778,858,920,1009,1074,1133,1206,1269,1323,1451,1508,1570,1624,1697,1840,1924,2012,2118,2206,2294,2379,2446,2512,2587,2663,2749,2826,2902,2979,3053,3144,3219,3310,3402,3476,3563,3654,3709,3775,3858,3944,4006,4070,4133,4250,4363,4474,4591,4677"}, "to": {"startLines": "2,36,38,39,40,45,46,63,66,68,69,70,71,72,73,74,75,76,77,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3412,3588,3683,3816,4359,4425,6613,6856,6987,7076,7141,7200,7273,7336,7390,7518,7575,7637,7691,7764,8136,8220,8308,8414,8502,8590,8675,8742,8808,8883,8959,9045,9122,9198,9275,9349,9440,9515,9606,9698,9772,9859,9950,10005,10071,10154,10240,10302,10366,10429,10546,10659,10770,10887", "endLines": "5,36,38,39,40,45,46,63,66,68,69,70,71,72,73,74,75,76,77,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,82,94,132,88,65,96,79,61,88,64,58,72,62,53,127,56,61,53,72,142,83,87,105,87,87,84,66,65,74,75,85,76,75,76,73,90,74,90,91,73,86,90,54,65,82,85,61,63,62,116,112,110,116,85", "endOffsets": "265,3490,3678,3811,3900,4420,4517,6688,6913,7071,7136,7195,7268,7331,7385,7513,7570,7632,7686,7759,7902,8215,8303,8409,8497,8585,8670,8737,8803,8878,8954,9040,9117,9193,9270,9344,9435,9510,9601,9693,9767,9854,9945,10000,10066,10149,10235,10297,10361,10424,10541,10654,10765,10882,10968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,218,302,375,450,538,607,674,754,836,923,1003,1074,1161,1248,1322,1401,1483,1560,1637,1712,1796,1871,1953,2023", "endColumns": "69,92,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "120,213,297,370,445,533,602,669,749,831,918,998,1069,1156,1243,1317,1396,1478,1555,1632,1707,1791,1866,1948,2018,2103"}, "to": {"startLines": "33,37,41,62,64,65,67,80,81,82,117,118,119,121,123,124,125,126,127,128,129,130,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3019,3495,3905,6540,6693,6768,6918,7907,7974,8054,10973,11060,11140,11358,11529,11616,11690,11769,11851,11928,12005,12080,12265,12340,12422,12492", "endColumns": "69,92,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "3084,3583,3984,6608,6763,6851,6982,7969,8049,8131,11055,11135,11206,11440,11611,11685,11764,11846,11923,12000,12075,12159,12335,12417,12487,12572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,242,378,492,654,748,917,1035,1148,1270,1400,1533,1669,1791,1954,2055,2235,2364,2498,2635,2766,2913,3023,3184,3291,3449,3553,3708", "endColumns": "186,135,113,161,93,168,117,112,121,129,132,135,121,162,100,179,128,133,136,130,146,109,160,106,157,103,154,125", "endOffsets": "237,373,487,649,743,912,1030,1143,1265,1395,1528,1664,1786,1949,2050,2230,2359,2493,2630,2761,2908,3018,3179,3286,3444,3548,3703,3829"}, "to": {"startLines": "34,35,42,43,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,120,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3089,3276,3989,4103,4265,4522,4691,4809,4922,5044,5174,5307,5443,5565,5728,5829,6009,6138,6272,6409,11211,12577,12687,12848,12955,13113,13217,13372", "endColumns": "186,135,113,161,93,168,117,112,121,129,132,135,121,162,100,179,128,133,136,130,146,109,160,106,157,103,154,125", "endOffsets": "3271,3407,4098,4260,4354,4686,4804,4917,5039,5169,5302,5438,5560,5723,5824,6004,6133,6267,6404,6535,11353,12682,12843,12950,13108,13212,13367,13493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,390,496,601,687,797,918,998,1075,1166,1259,1354,1448,1548,1641,1736,1844,1935,2026,2109,2223,2331,2431,2545,2652,2760,2920,11445", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "385,491,596,682,792,913,993,1070,1161,1254,1349,1443,1543,1636,1731,1839,1930,2021,2104,2218,2326,2426,2540,2647,2755,2915,3014,11524"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "12164", "endColumns": "100", "endOffsets": "12260"}}]}]}