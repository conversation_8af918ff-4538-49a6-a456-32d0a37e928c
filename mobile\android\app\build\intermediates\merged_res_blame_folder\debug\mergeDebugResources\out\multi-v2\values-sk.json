{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "383,490,591,702,788,896,1014,1093,1170,1261,1354,1452,1546,1646,1739,1834,1932,2023,2114,2198,2303,2411,2510,2616,2728,2831,2997,11310", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "485,586,697,783,891,1009,1088,1165,1256,1349,1447,1541,1641,1734,1829,1927,2018,2109,2193,2298,2406,2505,2611,2723,2826,2992,3090,11388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "134", "startColumns": "4", "startOffsets": "12028", "endColumns": "100", "endOffsets": "12124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,413,484,555,642,710,779,860,941,1028,1123,1197,1283,1367,1444,1525,1607,1685,1760,1834,1918,1989,2068,2139", "endColumns": "74,110,88,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "125,236,325,408,479,550,637,705,774,855,936,1023,1118,1192,1278,1362,1439,1520,1602,1680,1755,1829,1913,1984,2063,2134,2217"}, "to": {"startLines": "35,39,40,44,65,67,68,70,83,84,85,120,121,122,124,126,127,128,129,130,131,132,133,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3095,3528,3639,4029,6559,6706,6777,6929,7885,7954,8035,10848,10935,11030,11224,11393,11477,11554,11635,11717,11795,11870,11944,12129,12200,12279,12350", "endColumns": "74,110,88,82,70,70,86,67,68,80,80,86,94,73,85,83,76,80,81,77,74,73,83,70,78,70,82", "endOffsets": "3165,3634,3723,4107,6625,6772,6859,6992,7949,8030,8111,10930,11025,11099,11305,11472,11549,11630,11712,11790,11865,11939,12023,12195,12274,12345,12428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,205,335,441,607,697,838,941,1056,1183,1332,1468,1591,1726,1859,1957,2114,2236,2371,2504,2618,2738,2841,2981,3066,3188,3290,3429", "endColumns": "149,129,105,165,89,140,102,114,126,148,135,122,134,132,97,156,121,134,132,113,119,102,139,84,121,101,138,102", "endOffsets": "200,330,436,602,692,833,936,1051,1178,1327,1463,1586,1721,1854,1952,2109,2231,2366,2499,2613,2733,2836,2976,3061,3183,3285,3424,3527"}, "to": {"startLines": "36,37,45,46,47,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,123,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3170,3320,4112,4218,4384,4638,4779,4882,4997,5124,5273,5409,5532,5667,5800,5898,6055,6177,6312,6445,11104,12433,12536,12676,12761,12883,12985,13124", "endColumns": "149,129,105,165,89,140,102,114,126,148,135,122,134,132,97,156,121,134,132,113,119,102,139,84,121,101,138,102", "endOffsets": "3315,3445,4213,4379,4469,4774,4877,4992,5119,5268,5404,5527,5662,5795,5893,6050,6172,6307,6440,6554,11219,12531,12671,12756,12878,12980,13119,13222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,333,411,503,631,712,777,876,952,1017,1107,1173,1227,1296,1356,1410,1527,1587,1649,1703,1775,1905,1992,2084,2193,2262,2340,2428,2495,2561,2633,2710,2793,2865,2942,3015,3086,3174,3246,3338,3434,3508,3582,3678,3730,3797,3884,3971,4033,4097,4160,4266,4362,4460,4558", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "328,406,498,626,707,772,871,947,1012,1102,1168,1222,1291,1351,1405,1522,1582,1644,1698,1770,1900,1987,2079,2188,2257,2335,2423,2490,2556,2628,2705,2788,2860,2937,3010,3081,3169,3241,3333,3429,3503,3577,3673,3725,3792,3879,3966,4028,4092,4155,4261,4357,4455,4553,4632"}, "to": {"startLines": "2,38,41,42,43,48,49,66,69,71,72,73,74,75,76,77,78,79,80,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3450,3728,3820,3948,4474,4539,6630,6864,6997,7087,7153,7207,7276,7336,7390,7507,7567,7629,7683,7755,8116,8203,8295,8404,8473,8551,8639,8706,8772,8844,8921,9004,9076,9153,9226,9297,9385,9457,9549,9645,9719,9793,9889,9941,10008,10095,10182,10244,10308,10371,10477,10573,10671,10769", "endLines": "7,38,41,42,43,48,49,66,69,71,72,73,74,75,76,77,78,79,80,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "378,3523,3815,3943,4024,4534,4633,6701,6924,7082,7148,7202,7271,7331,7385,7502,7562,7624,7678,7750,7880,8198,8290,8399,8468,8546,8634,8701,8767,8839,8916,8999,9071,9148,9221,9292,9380,9452,9544,9640,9714,9788,9884,9936,10003,10090,10177,10239,10303,10366,10472,10568,10666,10764,10843"}}]}]}