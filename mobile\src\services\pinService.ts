import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

const PIN_STORAGE_KEY = '@autoflow_user_pin';
const USER_DATA_KEY = '@autoflow_user_data';

export interface UserData {
  email: string;
  userId: string;
  name?: string;
}

export class PinService {
  // Check if PIN is set up on device
  async isPinSetup(): Promise<boolean> {
    try {
      const pin = await AsyncStorage.getItem(PIN_STORAGE_KEY);
      return pin !== null;
    } catch (error) {
      console.error('Error checking PIN setup:', error);
      return false;
    }
  }

  // Save PIN securely on device
  async savePin(pin: string, userData: UserData): Promise<boolean> {
    try {
      if (!pin || pin.length < 4) {
        Alert.alert('Error', 'PIN must be at least 4 digits');
        return false;
      }

      // Hash the PIN for security (simple hash for demo)
      const hashedPin = this.hashPin(pin);
      
      await AsyncStorage.setItem(PIN_STORAGE_KEY, hashedPin);
      await AsyncStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
      
      console.log('✅ PIN saved successfully');
      return true;
    } catch (error) {
      console.error('❌ Error saving PIN:', error);
      Alert.alert('Error', 'Failed to save PIN. Please try again.');
      return false;
    }
  }

  // Verify PIN for login
  async verifyPin(enteredPin: string): Promise<UserData | null> {
    try {
      const storedHashedPin = await AsyncStorage.getItem(PIN_STORAGE_KEY);
      const userData = await AsyncStorage.getItem(USER_DATA_KEY);

      if (!storedHashedPin || !userData) {
        return null;
      }

      const hashedEnteredPin = this.hashPin(enteredPin);
      
      if (hashedEnteredPin === storedHashedPin) {
        console.log('✅ PIN verification successful');
        return JSON.parse(userData);
      } else {
        console.log('❌ PIN verification failed');
        return null;
      }
    } catch (error) {
      console.error('❌ Error verifying PIN:', error);
      return null;
    }
  }

  // Get stored user data
  async getUserData(): Promise<UserData | null> {
    try {
      const userData = await AsyncStorage.getItem(USER_DATA_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  // Clear PIN and user data (logout)
  async clearPin(): Promise<void> {
    try {
      await AsyncStorage.removeItem(PIN_STORAGE_KEY);
      await AsyncStorage.removeItem(USER_DATA_KEY);
      console.log('✅ PIN and user data cleared');
    } catch (error) {
      console.error('❌ Error clearing PIN:', error);
    }
  }

  // Simple hash function for PIN (in production, use proper encryption)
  private hashPin(pin: string): string {
    let hash = 0;
    for (let i = 0; i < pin.length; i++) {
      const char = pin.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  // Change existing PIN
  async changePin(oldPin: string, newPin: string): Promise<boolean> {
    try {
      const userData = await this.verifyPin(oldPin);
      if (!userData) {
        Alert.alert('Error', 'Current PIN is incorrect');
        return false;
      }

      return await this.savePin(newPin, userData);
    } catch (error) {
      console.error('❌ Error changing PIN:', error);
      Alert.alert('Error', 'Failed to change PIN. Please try again.');
      return false;
    }
  }
}

export const pinService = new PinService();
