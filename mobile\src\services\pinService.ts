import { Alert } from 'react-native';
import CryptoJS from 'crypto-js';

// Temporary in-memory storage for testing
// NOTE: This is a temporary solution to avoid AsyncStorage native module issues
// In production, this should be replaced with proper persistent storage
let tempPinStorage: string | null = null;
let tempUserDataStorage: UserData | null = null;

export interface UserData {
  email: string;
  userId: string;
  name?: string;
}

export class PinService {
  // Check if PIN is set up on device
  async isPinSetup(): Promise<boolean> {
    try {
      return tempPinStorage !== null;
    } catch (error) {
      console.error('Error checking PIN setup:', error);
      return false;
    }
  }

  // Save PIN securely on device
  async savePin(pin: string, userData: UserData): Promise<boolean> {
    try {
      if (!pin || pin.length < 4) {
        Alert.alert('Error', 'PIN must be at least 4 digits');
        return false;
      }

      // Hash the PIN for security
      const hashedPin = CryptoJS.SHA256(pin).toString();

      // Store in temporary memory (for testing)
      tempPinStorage = hashedPin;
      tempUserDataStorage = userData;

      console.log('✅ PIN saved successfully (temporary storage)');
      return true;
    } catch (error) {
      console.error('❌ Error saving PIN:', error);
      Alert.alert('Error', 'Failed to save PIN. Please try again.');
      return false;
    }
  }

  // Verify PIN for login
  async verifyPin(enteredPin: string): Promise<UserData | null> {
    try {
      if (!tempPinStorage || !tempUserDataStorage) {
        return null;
      }

      const hashedEnteredPin = CryptoJS.SHA256(enteredPin).toString();

      if (hashedEnteredPin === tempPinStorage) {
        console.log('✅ PIN verification successful');
        return tempUserDataStorage;
      } else {
        console.log('❌ PIN verification failed');
        return null;
      }
    } catch (error) {
      console.error('❌ Error verifying PIN:', error);
      return null;
    }
  }

  // Get stored user data
  async getUserData(): Promise<UserData | null> {
    try {
      return tempUserDataStorage;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  // Clear PIN and user data (logout)
  async clearPin(): Promise<void> {
    try {
      tempPinStorage = null;
      tempUserDataStorage = null;
      console.log('✅ PIN and user data cleared');
    } catch (error) {
      console.error('❌ Error clearing PIN:', error);
    }
  }



  // Change existing PIN
  async changePin(oldPin: string, newPin: string): Promise<boolean> {
    try {
      const userData = await this.verifyPin(oldPin);
      if (!userData) {
        Alert.alert('Error', 'Current PIN is incorrect');
        return false;
      }

      return await this.savePin(newPin, userData);
    } catch (error) {
      console.error('❌ Error changing PIN:', error);
      Alert.alert('Error', 'Failed to change PIN. Please try again.');
      return false;
    }
  }
}

export const pinService = new PinService();
