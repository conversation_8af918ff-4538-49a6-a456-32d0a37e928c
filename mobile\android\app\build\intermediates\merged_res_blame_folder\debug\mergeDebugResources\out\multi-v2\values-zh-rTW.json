{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,269,363,470,543,605,683,743,803,881,939,995,1055,1113,1167,1252,1308,1366,1420,1485,1577,1651,1728,1818,1881,1944,2021,2088,2154,2217,2285,2363,2424,2495,2562,2624,2703,2768,2851,2936,3010,3074,3150,3198,3262,3338,3416,3478,3542,3605,3685,3762,3838,3915", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,66,93,106,72,61,77,59,59,77,57,55,59,57,53,84,55,57,53,64,91,73,76,89,62,62,76,66,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,68", "endOffsets": "197,264,358,465,538,600,678,738,798,876,934,990,1050,1108,1162,1247,1303,1361,1415,1480,1572,1646,1723,1813,1876,1939,2016,2083,2149,2212,2280,2358,2419,2490,2557,2619,2698,2763,2846,2931,3005,3069,3145,3193,3257,3333,3411,3473,3537,3600,3680,3757,3833,3910,3979"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3112,3359,3453,3560,4008,4070,5769,5970,6095,6173,6231,6287,6347,6405,6459,6544,6600,6658,6712,6777,7077,7151,7228,7318,7381,7444,7521,7588,7654,7717,7785,7863,7924,7995,8062,8124,8203,8268,8351,8436,8510,8574,8650,8698,8762,8838,8916,8978,9042,9105,9185,9262,9338,9415", "endLines": "5,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "12,66,93,106,72,61,77,59,59,77,57,55,59,57,53,84,55,57,53,64,91,73,76,89,62,62,76,66,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,68", "endOffsets": "247,3174,3448,3555,3628,4065,4143,5824,6025,6168,6226,6282,6342,6400,6454,6539,6595,6653,6707,6772,6864,7146,7223,7313,7376,7439,7516,7583,7649,7712,7780,7858,7919,7990,8057,8119,8198,8263,8346,8431,8505,8569,8645,8693,8757,8833,8911,8973,9037,9100,9180,9257,9333,9410,9479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,218,301,373,440,506,581,646,712,782,854,927,1002,1069,1139,1212,1284,1361,1437,1509,1579,1648,1728,1796,1866,1933", "endColumns": "65,96,82,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,213,296,368,435,501,576,641,707,777,849,922,997,1064,1134,1207,1279,1356,1432,1504,1574,1643,1723,1791,1861,1928,1997"}, "to": {"startLines": "33,37,38,42,63,65,66,68,81,82,83,118,119,120,122,124,125,126,127,128,129,130,131,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2818,3179,3276,3633,5702,5829,5895,6030,6869,6935,7005,9484,9557,9632,9799,9948,10021,10093,10170,10246,10318,10388,10457,10638,10706,10776,10843", "endColumns": "65,96,82,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2879,3271,3354,3700,5764,5890,5965,6090,6930,7000,7072,9552,9627,9694,9864,10016,10088,10165,10241,10313,10383,10452,10532,10701,10771,10838,10907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "252,347,440,540,622,719,827,904,979,1071,1165,1262,1358,1453,1547,1643,1735,1827,1919,1997,2093,2188,2283,2380,2476,2574,2724,9869", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "342,435,535,617,714,822,899,974,1066,1160,1257,1353,1448,1542,1638,1730,1822,1914,1992,2088,2183,2278,2375,2471,2569,2719,2813,9943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "10537", "endColumns": "100", "endOffsets": "10633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,283,381,504,586,704,800,899,998,1101,1204,1302,1403,1507,1597,1721,1823,1935,2042,2140,2240,2327,2434,2516,2618,2707,2816", "endColumns": "123,103,97,122,81,117,95,98,98,102,102,97,100,103,89,123,101,111,106,97,99,86,106,81,101,88,108,88", "endOffsets": "174,278,376,499,581,699,795,894,993,1096,1199,1297,1398,1502,1592,1716,1818,1930,2037,2135,2235,2322,2429,2511,2613,2702,2811,2900"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,121,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2884,3008,3705,3803,3926,4148,4266,4362,4461,4560,4663,4766,4864,4965,5069,5159,5283,5385,5497,5604,9699,10912,10999,11106,11188,11290,11379,11488", "endColumns": "123,103,97,122,81,117,95,98,98,102,102,97,100,103,89,123,101,111,106,97,99,86,106,81,101,88,108,88", "endOffsets": "3003,3107,3798,3921,4003,4261,4357,4456,4555,4658,4761,4859,4960,5064,5154,5278,5380,5492,5599,5697,9794,10994,11101,11183,11285,11374,11483,11572"}}]}]}