{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,135,204,285,355,431", "endColumns": "79,68,80,69,75,74", "endOffsets": "130,199,280,350,426,501"}, "to": {"startLines": "39,61,62,64,113,114", "startColumns": "4,4,4,4,4,4", "startOffsets": "3634,6324,6393,6533,10483,10559", "endColumns": "79,68,80,69,75,74", "endOffsets": "3709,6388,6469,6598,10554,10629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,346,455,620,721,879,999,1120,1241,1376,1514,1650,1778,1928,2022,2193,2326,2460,2596,2712,2849,2945,3078,3166,3292,3393,3532", "endColumns": "164,125,108,164,100,157,119,120,120,134,137,135,127,149,93,170,132,133,135,115,136,95,132,87,125,100,138,105", "endOffsets": "215,341,450,615,716,874,994,1115,1236,1371,1509,1645,1773,1923,2017,2188,2321,2455,2591,2707,2844,2940,3073,3161,3287,3388,3527,3633"}, "to": {"startLines": "33,34,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,111,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2962,3127,3714,3823,3988,4252,4410,4530,4651,4772,4907,5045,5181,5309,5459,5553,5724,5857,5991,6127,10265,10735,10831,10964,11052,11178,11279,11418", "endColumns": "164,125,108,164,100,157,119,120,120,134,137,135,127,149,93,170,132,133,135,115,136,95,132,87,125,100,138,105", "endOffsets": "3122,3248,3818,3983,4084,4405,4525,4646,4767,4902,5040,5176,5304,5454,5548,5719,5852,5986,6122,6238,10397,10826,10959,11047,11173,11274,11413,11519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,384,484,593,679,785,899,982,1063,1154,1247,1342,1438,1535,1628,1722,1814,1905,1995,2075,2182,2285,2382,2489,2591,2704,2863,10402", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "379,479,588,674,780,894,977,1058,1149,1242,1337,1433,1530,1623,1717,1809,1900,1990,2070,2177,2280,2377,2484,2586,2699,2858,2957,10478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "10634", "endColumns": "100", "endOffsets": "10730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,299,397,516,601,666,764,845,904,997,1060,1118,1189,1251,1305,1426,1483,1544,1598,1669,1802,1886,1969,2072,2154,2232,2322,2389,2455,2526,2604,2690,2765,2843,2923,3006,3094,3173,3263,3356,3430,3500,3591,3645,3712,3796,3881,3943,4007,4070,4174,4280,4377,4482", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,97,118,84,64,97,80,58,92,62,57,70,61,53,120,56,60,53,70,132,83,82,102,81,77,89,66,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,83", "endOffsets": "215,294,392,511,596,661,759,840,899,992,1055,1113,1184,1246,1300,1421,1478,1539,1593,1664,1797,1881,1964,2067,2149,2227,2317,2384,2450,2521,2599,2685,2760,2838,2918,3001,3089,3168,3258,3351,3425,3495,3586,3640,3707,3791,3876,3938,4002,4065,4169,4275,4372,4477,4561"}, "to": {"startLines": "2,35,36,37,38,43,44,60,63,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3253,3332,3430,3549,4089,4154,6243,6474,6603,6696,6759,6817,6888,6950,7004,7125,7182,7243,7297,7368,7501,7585,7668,7771,7853,7931,8021,8088,8154,8225,8303,8389,8464,8542,8622,8705,8793,8872,8962,9055,9129,9199,9290,9344,9411,9495,9580,9642,9706,9769,9873,9979,10076,10181", "endLines": "5,35,36,37,38,43,44,60,63,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "12,78,97,118,84,64,97,80,58,92,62,57,70,61,53,120,56,60,53,70,132,83,82,102,81,77,89,66,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,83", "endOffsets": "265,3327,3425,3544,3629,4149,4247,6319,6528,6691,6754,6812,6883,6945,6999,7120,7177,7238,7292,7363,7496,7580,7663,7766,7848,7926,8016,8083,8149,8220,8298,8384,8459,8537,8617,8700,8788,8867,8957,9050,9124,9194,9285,9339,9406,9490,9575,9637,9701,9764,9868,9974,10071,10176,10260"}}]}]}