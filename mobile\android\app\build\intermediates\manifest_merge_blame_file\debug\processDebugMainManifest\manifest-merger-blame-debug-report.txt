1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.autoflowmobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:5-67
11-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
12-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:4:5-74
12-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:4:22-71
13    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
13-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:5-72
13-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:5:22-69
14    <uses-permission android:name="android.permission.USE_FACE_ID" /> <!-- Hardware features -->
14-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:6:5-70
14-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:6:22-67
15    <uses-feature
15-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:5-90
16        android:name="android.hardware.fingerprint"
16-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:19-62
17        android:required="false" />
17-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:9:63-87
18    <uses-feature
18-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:5-89
19        android:name="android.hardware.biometrics"
19-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:19-61
20        android:required="false" />
20-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:10:62-86
21
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
22-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
23    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
23-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
23-->[:react-native-community_netinfo] C:\Users\<USER>\Node\autoflow\mobile\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
24    <!--
25    This manifest file is used only by Gradle to configure debug-only capabilities
26    for React Native Apps.
27    -->
28    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
28-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:5-78
28-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:16:22-75
29
30    <permission
30-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c1b70f4575737819a52ab311ac30c67\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
31        android:name="com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c1b70f4575737819a52ab311ac30c67\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c1b70f4575737819a52ab311ac30c67\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.autoflowmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c1b70f4575737819a52ab311ac30c67\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c1b70f4575737819a52ab311ac30c67\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
35
36    <application
36-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:12:5-31:19
37        android:name="com.autoflowmobile.MainApplication"
37-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:13:7-38
38        android:allowBackup="false"
38-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:17:7-34
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9c1b70f4575737819a52ab311ac30c67\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:extractNativeLibs="true"
42        android:icon="@mipmap/ic_launcher"
42-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:15:7-41
43        android:label="@string/app_name"
43-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:14:7-39
44        android:roundIcon="@mipmap/ic_launcher_round"
44-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:16:7-52
45        android:theme="@style/AppTheme"
45-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:18:7-38
46        android:usesCleartextTraffic="true" >
46-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\debug\AndroidManifest.xml:6:9-44
47        <activity
47-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:19:7-30:18
48            android:name="com.autoflowmobile.MainActivity"
48-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:20:9-37
49            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
49-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:22:9-118
50            android:exported="true"
50-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:25:9-32
51            android:label="@string/app_name"
51-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:21:9-41
52            android:launchMode="singleTask"
52-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:23:9-40
53            android:windowSoftInputMode="adjustResize" >
53-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:24:9-51
54            <intent-filter>
54-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:26:9-29:25
55                <action android:name="android.intent.action.MAIN" />
55-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:27:13-65
55-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:27:21-62
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:28:13-73
57-->C:\Users\<USER>\Node\autoflow\mobile\android\app\src\main\AndroidManifest.xml:28:23-70
58            </intent-filter>
59        </activity>
60        <activity
60-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:19:9-21:40
61            android:name="com.facebook.react.devsupport.DevSettingsActivity"
61-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:20:13-77
62            android:exported="false" />
62-->[com.facebook.react:react-android:0.73.2] C:\Users\<USER>\.gradle\caches\transforms-4\58af14fb467dc5279be96a60d61668ae\transformed\jetified-react-android-0.73.2-debug\AndroidManifest.xml:21:13-37
63
64        <provider
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
65            android:name="androidx.startup.InitializationProvider"
65-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
66            android:authorities="com.autoflowmobile.androidx-startup"
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
67            android:exported="false" >
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
68            <meta-data
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.emoji2.text.EmojiCompatInitializer"
69-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
70                android:value="androidx.startup" />
70-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\53b4f45972105f31ab44fccde1b707fa\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\27ea7f128c389bb9aa8f4b759cf30146\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
72-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\27ea7f128c389bb9aa8f4b759cf30146\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
73                android:value="androidx.startup" />
73-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\27ea7f128c389bb9aa8f4b759cf30146\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
76                android:value="androidx.startup" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
77        </provider>
78
79        <receiver
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
80            android:name="androidx.profileinstaller.ProfileInstallReceiver"
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
81            android:directBootAware="false"
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
82            android:enabled="true"
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
83            android:exported="true"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
84            android:permission="android.permission.DUMP" >
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
86                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
89                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
92                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
95                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\225d08932df59335efebd3aba47e4802\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
96            </intent-filter>
97        </receiver>
98
99        <meta-data
99-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
100            android:name="com.facebook.soloader.enabled"
100-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:13:13-57
101            android:value="false" />
101-->[com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-4\70e7427778ad22106260fb80a7b7054c\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
102    </application>
103
104</manifest>
