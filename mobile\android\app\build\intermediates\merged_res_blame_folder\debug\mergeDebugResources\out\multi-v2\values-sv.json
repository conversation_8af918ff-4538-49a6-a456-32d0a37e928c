{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "271,374,477,588,672,774,887,964,1039,1132,1227,1322,1416,1518,1613,1710,1808,1904,1997,2077,2183,2282,2378,2483,2586,2688,2842,10957", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "369,472,583,667,769,882,959,1034,1127,1222,1317,1411,1513,1608,1705,1803,1899,1992,2072,2178,2277,2373,2478,2581,2683,2837,2939,11032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "11660", "endColumns": "100", "endOffsets": "11756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,294,365,433,514,581,648,722,799,881,960,1029,1111,1194,1271,1354,1433,1510,1580,1649,1734,1814,1889", "endColumns": "72,82,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "123,206,289,360,428,509,576,643,717,794,876,955,1024,1106,1189,1266,1349,1428,1505,1575,1644,1729,1809,1884,1962"}, "to": {"startLines": "33,37,41,62,64,65,67,80,81,82,117,118,119,121,123,124,125,126,127,128,129,130,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2944,3382,3764,6301,6436,6504,6645,7581,7648,7722,10527,10609,10688,10875,11037,11120,11197,11280,11359,11436,11506,11575,11761,11841,11916", "endColumns": "72,82,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "3012,3460,3842,6367,6499,6580,6707,7643,7717,7794,10604,10683,10752,10952,11115,11192,11275,11354,11431,11501,11570,11655,11836,11911,11989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,302,400,522,601,664,756,820,880,972,1035,1097,1164,1228,1282,1387,1446,1507,1561,1630,1749,1832,1916,2022,2101,2185,2271,2338,2404,2473,2547,2636,2708,2785,2856,2930,3021,3100,3187,3275,3347,3421,3506,3557,3624,3705,3789,3851,3915,3978,4085,4192,4291,4399", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,97,121,78,62,91,63,59,91,62,61,66,63,53,104,58,60,53,68,118,82,83,105,78,83,85,66,65,68,73,88,71,76,70,73,90,78,86,87,71,73,84,50,66,80,83,61,63,62,106,106,98,107,77", "endOffsets": "216,297,395,517,596,659,751,815,875,967,1030,1092,1159,1223,1277,1382,1441,1502,1556,1625,1744,1827,1911,2017,2096,2180,2266,2333,2399,2468,2542,2631,2703,2780,2851,2925,3016,3095,3182,3270,3342,3416,3501,3552,3619,3700,3784,3846,3910,3973,4080,4187,4286,4394,4472"}, "to": {"startLines": "2,36,38,39,40,45,46,63,66,68,69,70,71,72,73,74,75,76,77,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3301,3465,3563,3685,4196,4259,6372,6585,6712,6804,6867,6929,6996,7060,7114,7219,7278,7339,7393,7462,7799,7882,7966,8072,8151,8235,8321,8388,8454,8523,8597,8686,8758,8835,8906,8980,9071,9150,9237,9325,9397,9471,9556,9607,9674,9755,9839,9901,9965,10028,10135,10242,10341,10449", "endLines": "5,36,38,39,40,45,46,63,66,68,69,70,71,72,73,74,75,76,77,78,79,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,80,97,121,78,62,91,63,59,91,62,61,66,63,53,104,58,60,53,68,118,82,83,105,78,83,85,66,65,68,73,88,71,76,70,73,90,78,86,87,71,73,84,50,66,80,83,61,63,62,106,106,98,107,77", "endOffsets": "266,3377,3558,3680,3759,4254,4346,6431,6640,6799,6862,6924,6991,7055,7109,7214,7273,7334,7388,7457,7576,7877,7961,8067,8146,8230,8316,8383,8449,8518,8592,8681,8753,8830,8901,8975,9066,9145,9232,9320,9392,9466,9551,9602,9669,9750,9834,9896,9960,10023,10130,10237,10336,10444,10522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,339,448,602,688,833,946,1063,1184,1321,1451,1573,1702,1838,1943,2101,2227,2369,2511,2638,2756,2850,2983,3076,3201,3307,3445", "endColumns": "157,125,108,153,85,144,112,116,120,136,129,121,128,135,104,157,125,141,141,126,117,93,132,92,124,105,137,97", "endOffsets": "208,334,443,597,683,828,941,1058,1179,1316,1446,1568,1697,1833,1938,2096,2222,2364,2506,2633,2751,2845,2978,3071,3196,3302,3440,3538"}, "to": {"startLines": "34,35,42,43,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,120,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3017,3175,3847,3956,4110,4351,4496,4609,4726,4847,4984,5114,5236,5365,5501,5606,5764,5890,6032,6174,10757,11994,12088,12221,12314,12439,12545,12683", "endColumns": "157,125,108,153,85,144,112,116,120,136,129,121,128,135,104,157,125,141,141,126,117,93,132,92,124,105,137,97", "endOffsets": "3170,3296,3951,4105,4191,4491,4604,4721,4842,4979,5109,5231,5360,5496,5601,5759,5885,6027,6169,6296,10870,12083,12216,12309,12434,12540,12678,12776"}}]}]}