{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "129", "startColumns": "4", "startOffsets": "12148", "endColumns": "100", "endOffsets": "12244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,320,420,542,627,693,790,870,932,1024,1098,1159,1238,1302,1356,1472,1531,1593,1647,1729,1858,1950,2034,2148,2227,2308,2401,2468,2534,2613,2694,2785,2857,2935,3010,3082,3179,3256,3354,3452,3530,3611,3711,3768,3834,3917,4004,4066,4130,4193,4295,4402,4499,4608", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "233,315,415,537,622,688,785,865,927,1019,1093,1154,1233,1297,1351,1467,1526,1588,1642,1724,1853,1945,2029,2143,2222,2303,2396,2463,2529,2608,2689,2780,2852,2930,3005,3077,3174,3251,3349,3447,3525,3606,3706,3763,3829,3912,3999,4061,4125,4188,4290,4397,4494,4603,4692"}, "to": {"startLines": "2,36,38,39,40,45,46,63,65,67,68,69,70,71,72,73,74,75,76,77,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3418,3588,3688,3810,4345,4411,6701,6864,6993,7085,7159,7220,7299,7363,7417,7533,7592,7654,7708,7790,8079,8171,8255,8369,8448,8529,8622,8689,8755,8834,8915,9006,9078,9156,9231,9303,9400,9477,9575,9673,9751,9832,9932,9989,10055,10138,10225,10287,10351,10414,10516,10623,10720,10829", "endLines": "5,36,38,39,40,45,46,63,65,67,68,69,70,71,72,73,74,75,76,77,78,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "283,3495,3683,3805,3890,4406,4503,6776,6921,7080,7154,7215,7294,7358,7412,7528,7587,7649,7703,7785,7914,8166,8250,8364,8443,8524,8617,8684,8750,8829,8910,9001,9073,9151,9226,9298,9395,9472,9570,9668,9746,9827,9927,9984,10050,10133,10220,10282,10346,10409,10511,10618,10715,10824,10913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,365,483,643,735,896,1011,1133,1254,1405,1559,1693,1824,2004,2107,2287,2421,2555,2718,2858,2993,3105,3263,3355,3493,3604,3761", "endColumns": "177,131,117,159,91,160,114,121,120,150,153,133,130,179,102,179,133,133,162,139,134,111,157,91,137,110,156,113", "endOffsets": "228,360,478,638,730,891,1006,1128,1249,1400,1554,1688,1819,1999,2102,2282,2416,2550,2713,2853,2988,3100,3258,3350,3488,3599,3756,3870"}, "to": {"startLines": "34,35,42,43,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,118,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3108,3286,3975,4093,4253,4508,4669,4784,4906,5027,5178,5332,5466,5597,5777,5880,6060,6194,6328,6491,11171,12557,12669,12827,12919,13057,13168,13325", "endColumns": "177,131,117,159,91,160,114,121,120,150,153,133,130,179,102,179,133,133,162,139,134,111,157,91,137,110,156,113", "endOffsets": "3281,3413,4088,4248,4340,4664,4779,4901,5022,5173,5327,5461,5592,5772,5875,6055,6189,6323,6486,6626,11301,12664,12822,12914,13052,13163,13320,13434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,213,293,363,446,513,592,673,763,855,926,1014,1109,1200,1280,1360,1443,1520,1593,1681,1753,1836,1909", "endColumns": "69,87,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "120,208,288,358,441,508,587,668,758,850,921,1009,1104,1195,1275,1355,1438,1515,1588,1676,1748,1831,1904,1984"}, "to": {"startLines": "33,37,41,62,64,66,79,80,115,116,117,119,121,122,123,124,125,126,127,128,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3038,3500,3895,6631,6781,6926,7919,7998,10918,11008,11100,11306,11481,11576,11667,11747,11827,11910,11987,12060,12249,12321,12404,12477", "endColumns": "69,87,79,69,82,66,78,80,89,91,70,87,94,90,79,79,82,76,72,87,71,82,72,79", "endOffsets": "3103,3583,3970,6696,6859,6988,7993,8074,11003,11095,11166,11389,11571,11662,11742,11822,11905,11982,12055,12143,12316,12399,12472,12552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,399,506,616,703,809,939,1024,1104,1195,1288,1386,1481,1581,1674,1767,1862,1953,2044,2130,2240,2351,2454,2565,2673,2780,2939,11394", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "394,501,611,698,804,934,1019,1099,1190,1283,1381,1476,1576,1669,1762,1857,1948,2039,2125,2235,2346,2449,2560,2668,2775,2934,3033,11476"}}]}]}