import { 
  ChallengeResponse, 
  VerificationRequest, 
  VerificationResponse 
} from '../types/biometric.types';

class ApiService {
  private baseUrl: string;
  private authToken: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  setAuthToken(token: string): void {
    this.authToken = token;
  }

  private async makeRequest<T>(
    endpoint: string, 
    method: 'GET' | 'POST' | 'DELETE' = 'GET', 
    body?: any
  ): Promise<T> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Request a cryptographic challenge for biometric authentication
   */
  async getBiometricChallenge(userId: string): Promise<ChallengeResponse> {
    // For demo purposes, return a mock challenge
    // In production, this would call your actual backend
    console.log('ApiService: Getting biometric challenge for user:', userId);
    
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        const challenge = Buffer.from(`challenge_${Date.now()}_${Math.random()}`).toString('base64');
        resolve({
          challenge,
          expiresAt: Date.now() + (5 * 60 * 1000) // 5 minutes
        });
      }, 500);
    });
  }

  /**
   * Verify biometric signature with the backend
   */
  async verifyBiometricSignature(request: VerificationRequest): Promise<VerificationResponse> {
    console.log('ApiService: Verifying biometric signature for user:', request.userId);

    try {
      const response = await this.makeRequest<VerificationResponse>('/api/auth/biometric-verify', 'POST', {
        userId: request.userId,
        challenge: request.challenge,
        signature: request.signature,
      });

      return response;
    } catch (error) {
      console.error('ApiService: Biometric verification failed:', error);
      return {
        success: false,
        token: null,
      };
    }
  }

  /**
   * Register a new biometric public key
   */
  async registerBiometricKey(userId: string, publicKey: string): Promise<{ success: boolean }> {
    console.log('ApiService: Registering biometric key for user:', userId);
    console.log('Public key length:', publicKey.length);

    try {
      const response = await this.makeRequest<{ success: boolean }>('/api/auth/biometric-register', 'POST', {
        userId,
        publicKey,
      });

      return response;
    } catch (error) {
      console.error('ApiService: Biometric key registration failed:', error);
      return { success: false };
    }
  }

  /**
   * Delete biometric public key from backend
   */
  async deleteBiometricKey(userId: string): Promise<{ success: boolean }> {
    console.log('ApiService: Deleting biometric key for user:', userId);

    try {
      const response = await this.makeRequest<{ success: boolean }>('/api/auth/biometric-delete', 'DELETE', {
        userId,
      });

      return response;
    } catch (error) {
      console.error('ApiService: Biometric key deletion failed:', error);
      return { success: false };
    }
  }
}

// Export singleton instance
// Use the real API base URL from config
import { CONFIG } from '../constants/config';
export const apiService = new ApiService(CONFIG.API_BASE_URL);
