// app/api/auth/biometric-login/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { email, userId } = await request.json();

    if (!email || !userId) {
      return NextResponse.json(
        { error: 'Email and userId are required' },
        { status: 400 }
      );
    }

    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response });

    // Verify the user exists and matches the provided email
    const { data: user, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError || !user) {
      console.error('Biometric login - user not found:', userError);
      return NextResponse.json(
        { error: 'Invalid user credentials' },
        { status: 401 }
      );
    }

    if (user.email !== email) {
      console.error('Biometric login - email mismatch');
      return NextResponse.json(
        { error: 'Invalid user credentials' },
        { status: 401 }
      );
    }

    // Create a session for the user
    const { data: sessionData, error: sessionError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: email,
    });

    if (sessionError || !sessionData) {
      console.error('Biometric login - session creation failed:', sessionError);
      return NextResponse.json(
        { error: 'Failed to create session' },
        { status: 500 }
      );
    }

    // Get user profile data
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.warn('Biometric login - profile fetch failed:', profileError);
    }

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        email_confirmed_at: user.email_confirmed_at,
        created_at: user.created_at,
        updated_at: user.updated_at,
        profile: profile || null,
      },
      session: sessionData,
    });

  } catch (error) {
    console.error('Biometric login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
