{"logs": [{"outputFile": "com.autoflowmobile.app-mergeDebugResources-48:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f78e616298a04923dd5d4d62c52d50b0\\transformed\\core-1.9.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "12053", "endColumns": "100", "endOffsets": "12149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8f3cc09420e8ccfe99f443c7659aadc9\\transformed\\appcompat-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2900"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,384,488,596,682,790,909,993,1074,1165,1258,1354,1448,1548,1641,1736,1832,1923,2014,2101,2207,2313,2414,2521,2633,2737,2893,11323", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,84", "endOffsets": "379,483,591,677,785,904,988,1069,1160,1253,1349,1443,1543,1636,1731,1827,1918,2009,2096,2202,2308,2409,2516,2628,2732,2888,2986,11403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b9c140c81985842af1c691a7017321e7\\transformed\\material-1.6.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,308,404,528,615,681,772,842,906,1009,1074,1134,1202,1265,1320,1448,1505,1567,1622,1697,1837,1924,2007,2110,2192,2277,2364,2431,2497,2570,2646,2735,2808,2884,2959,3029,3117,3192,3284,3376,3450,3524,3616,3669,3736,3819,3906,3968,4032,4095,4209,4316,4418,4529", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,95,123,86,65,90,69,63,102,64,59,67,62,54,127,56,61,54,74,139,86,82,102,81,84,86,66,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,84", "endOffsets": "221,303,399,523,610,676,767,837,901,1004,1069,1129,1197,1260,1315,1443,1500,1562,1617,1692,1832,1919,2002,2105,2187,2272,2359,2426,2492,2565,2641,2730,2803,2879,2954,3024,3112,3187,3279,3371,3445,3519,3611,3664,3731,3814,3901,3963,4027,4090,4204,4311,4413,4524,4609"}, "to": {"startLines": "2,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3368,3656,3752,3876,4417,4483,6574,6795,6927,7030,7095,7155,7223,7286,7341,7469,7526,7588,7643,7718,8084,8171,8254,8357,8439,8524,8611,8678,8744,8817,8893,8982,9055,9131,9206,9276,9364,9439,9531,9623,9697,9771,9863,9916,9983,10066,10153,10215,10279,10342,10456,10563,10665,10776", "endLines": "5,36,39,40,41,46,47,64,67,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "12,81,95,123,86,65,90,69,63,102,64,59,67,62,54,127,56,61,54,74,139,86,82,102,81,84,86,66,65,72,75,88,72,75,74,69,87,74,91,91,73,73,91,52,66,82,86,61,63,62,113,106,101,110,84", "endOffsets": "271,3445,3747,3871,3958,4478,4569,6639,6854,7025,7090,7150,7218,7281,7336,7464,7521,7583,7638,7713,7853,8166,8249,8352,8434,8519,8606,8673,8739,8812,8888,8977,9050,9126,9201,9271,9359,9434,9526,9618,9692,9766,9858,9911,9978,10061,10148,10210,10274,10337,10451,10558,10660,10771,10856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\58af14fb467dc5279be96a60d61668ae\\transformed\\jetified-react-android-0.73.2-debug\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,248,339,422,495,564,646,714,781,857,940,1027,1107,1180,1264,1348,1425,1506,1588,1664,1741,1816,1909,1981,2065,2135", "endColumns": "77,114,90,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "128,243,334,417,490,559,641,709,776,852,935,1022,1102,1175,1259,1343,1420,1501,1583,1659,1736,1811,1904,1976,2060,2130,2211"}, "to": {"startLines": "33,37,38,42,63,65,66,68,81,82,83,118,119,120,122,124,125,126,127,128,129,130,131,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2991,3450,3565,3963,6501,6644,6713,6859,7858,7925,8001,10861,10948,11028,11239,11408,11492,11569,11650,11732,11808,11885,11960,12154,12226,12310,12380", "endColumns": "77,114,90,82,72,68,81,67,66,75,82,86,79,72,83,83,76,80,81,75,76,74,92,71,83,69,80", "endOffsets": "3064,3560,3651,4041,6569,6708,6790,6922,7920,7996,8079,10943,11023,11096,11318,11487,11564,11645,11727,11803,11880,11955,12048,12221,12305,12375,12456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\16801ecebf58528519bdc0bd327347a9\\transformed\\biometric-1.4.0-alpha04\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,354,471,632,725,886,1005,1118,1239,1369,1493,1622,1740,1875,1976,2145,2272,2402,2529,2652,2790,2887,3018,3103,3225,3323,3458", "endColumns": "167,130,116,160,92,160,118,112,120,129,123,128,117,134,100,168,126,129,126,122,137,96,130,84,121,97,134,105", "endOffsets": "218,349,466,627,720,881,1000,1113,1234,1364,1488,1617,1735,1870,1971,2140,2267,2397,2524,2647,2785,2882,3013,3098,3220,3318,3453,3559"}, "to": {"startLines": "34,35,43,44,45,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,121,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3069,3237,4046,4163,4324,4574,4735,4854,4967,5088,5218,5342,5471,5589,5724,5825,5994,6121,6251,6378,11101,12461,12558,12689,12774,12896,12994,13129", "endColumns": "167,130,116,160,92,160,118,112,120,129,123,128,117,134,100,168,126,129,126,122,137,96,130,84,121,97,134,105", "endOffsets": "3232,3363,4158,4319,4412,4730,4849,4962,5083,5213,5337,5466,5584,5719,5820,5989,6116,6246,6373,6496,11234,12553,12684,12769,12891,12989,13124,13230"}}]}]}